import * as fabric from "fabric";

export type AnnotationTool =
  | "rectangle"
  | "circle"
  | "ellipse"
  | "line"
  | "polygon"
  | "triangle";

interface AnnotationState {
  currentTool: AnnotationTool | null;
  isDrawing: boolean;
  startPoint: { x: number; y: number } | null;
  polygonPoints: { x: number; y: number }[];
  activeCanvasId: string | null;
}

const annotationState: AnnotationState = {
  currentTool: null,
  isDrawing: false,
  startPoint: null,
  polygonPoints: [],
  activeCanvasId: null,
};

const canvasInstances = new Map<string, fabric.Canvas>();

function getActiveCanvas(): fabric.Canvas | null {
  if (!annotationState.activeCanvasId) return null;

  let canvas = canvasInstances.get(annotationState.activeCanvasId);

  // For StackViewer, try to get the canvas from window
  if (!canvas && annotationState.activeCanvasId === "stack-viewer-container") {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    canvas = (window as any).stackViewerFabricCanvas;
  }

  // For ImageViewer, try to get the canvas from window
  if (!canvas && annotationState.activeCanvasId === "image-viewer-container") {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    canvas = (window as any).imageViewerFabricCanvas;
  }

  return canvas || null;
}

export function initializeAnnotations(
  targetElement: HTMLElement,
  containerId: string
) {
  const container = document.getElementById(containerId);
  if (!container) return null;

  // Remove existing canvas if any
  removeAnnotations(containerId);

  if (targetElement instanceof HTMLImageElement) {
    // For regular images, use the image as background
    return initializeImageAnnotations(targetElement, container, containerId);
  } else if (targetElement instanceof HTMLCanvasElement) {
    // For DICOM canvases, create overlay approach
    return initializeDicomAnnotations(targetElement, container, containerId);
  }

  return null;
}

function initializeImageAnnotations(
  imageElement: HTMLImageElement,
  container: HTMLElement,
  containerId: string
) {
  const canvas = document.createElement("canvas");
  canvas.id = `${containerId}-annotations`;
  canvas.style.position = "absolute";
  canvas.style.top = "0";
  canvas.style.left = "0";
  canvas.style.width = "100%";
  canvas.style.height = "100%";
  canvas.style.pointerEvents = "auto";
  canvas.style.zIndex = "10";

  if (container.style.position !== "relative") {
    container.style.position = "relative";
  }

  container.appendChild(canvas);

  const fabricCanvas = new fabric.Canvas(canvas.id, {
    selection: false,
    isDrawingMode: false,
    backgroundColor: "transparent",
  });

  canvasInstances.set(containerId, fabricCanvas);

  // Store reference for ImageViewer filters
  if (containerId === "image-viewer-container") {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerFabricCanvas = fabricCanvas;
  }

  // Load image as Fabric object (not background) so filters can be applied
  if (imageElement.src) {
    console.log("🖼️ Loading image into Fabric.js:", imageElement.src);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (fabric as any).Image.fromURL(
      imageElement.src,
      (img: any) => {
        if (!img) {
          console.error("❌ Failed to load image into Fabric.js");
          return;
        }

        console.log("✅ Image loaded into Fabric.js:", {
          width: img.width,
          height: img.height,
        });

        const rect = container.getBoundingClientRect();
        console.log("📐 Container dimensions:", rect);

        // Set canvas dimensions first
        fabricCanvas.setDimensions({ width: rect.width, height: rect.height });

        const scaleX = rect.width / (img.width || 1);
        const scaleY = rect.height / (img.height || 1);
        const scale = Math.min(scaleX, scaleY);

        img.set({
          scaleX: scale,
          scaleY: scale,
          left: (rect.width - (img.width || 0) * scale) / 2,
          top: (rect.height - (img.height || 0) * scale) / 2,
          selectable: false,
          evented: false,
        });

        console.log("🎯 Image positioned:", {
          scale,
          left: img.left,
          top: img.top,
          finalWidth: img.width * scale,
          finalHeight: img.height * scale,
        });

        // Add as regular object, not background, so filters work
        fabricCanvas.add(img);

        // Store reference to the image object for filter application
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (fabricCanvas as any).imageObject = img;

        fabricCanvas.renderAll();
        console.log("✅ Fabric.js canvas rendered with image");
      },
      { crossOrigin: "anonymous" } // Needed for CORS-safe images when applying filters
    );
  }

  return canvas;
}

function initializeDicomAnnotations(
  dicomCanvas: HTMLCanvasElement,
  container: HTMLElement,
  containerId: string
) {
  // Create annotation canvas that overlays the DICOM canvas
  const annotationCanvas = document.createElement("canvas");
  annotationCanvas.id = `${containerId}-annotations`;
  annotationCanvas.style.position = "absolute";
  annotationCanvas.style.left = "0";
  annotationCanvas.style.top = "0";
  annotationCanvas.style.pointerEvents = "auto";
  annotationCanvas.style.zIndex = "1";
  annotationCanvas.style.backgroundColor = "transparent";

  // Ensure container is positioned
  if (container.style.position !== "relative") {
    container.style.position = "relative";
  }

  // Insert the annotation canvas right after the DICOM element
  const dicomElement = dicomCanvas.parentElement;
  if (dicomElement && dicomElement.parentElement === container) {
    container.insertBefore(annotationCanvas, dicomElement.nextSibling);
  } else {
    container.appendChild(annotationCanvas);
  }

  const fabricCanvas = new fabric.Canvas(annotationCanvas.id, {
    selection: false,
    isDrawingMode: false,
    backgroundColor: "transparent",
  });

  canvasInstances.set(containerId, fabricCanvas);

  // Function to sync canvas size with DICOM canvas
  const syncCanvasSize = () => {
    const dicomRect = dicomCanvas.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    // Position annotation canvas exactly over DICOM canvas
    annotationCanvas.style.left = `${dicomRect.left - containerRect.left}px`;
    annotationCanvas.style.top = `${dicomRect.top - containerRect.top}px`;
    annotationCanvas.style.width = `${dicomRect.width}px`;
    annotationCanvas.style.height = `${dicomRect.height}px`;

    // Set actual canvas dimensions
    annotationCanvas.width = dicomRect.width;
    annotationCanvas.height = dicomRect.height;

    fabricCanvas.setDimensions({
      width: dicomRect.width,
      height: dicomRect.height,
    });

    fabricCanvas.renderAll();
  };

  // Initial sync
  setTimeout(syncCanvasSize, 100);

  // Listen for Cornerstone image rendered events to sync transformations
  if (dicomElement) {
    dicomElement.addEventListener("cornerstoneimagerendered", () => {
      syncCanvasSize();
      // TODO: Sync zoom/pan transformations if needed
    });
  }

  // Resize observer for dynamic resizing
  const resizeObserver = new ResizeObserver(syncCanvasSize);
  resizeObserver.observe(dicomCanvas);

  // Store observer for cleanup
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (annotationCanvas as any)._resizeObserver = resizeObserver;

  return annotationCanvas;
}

export function setAnnotationTool(tool: AnnotationTool, containerId: string) {
  let fabricCanvas = canvasInstances.get(containerId);

  // For StackViewer, try to get the canvas from window
  if (!fabricCanvas && containerId === "stack-viewer-container") {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    fabricCanvas = (window as any).stackViewerFabricCanvas;
  }

  // For ImageViewer, try to get the canvas from window
  if (!fabricCanvas && containerId === "image-viewer-container") {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    fabricCanvas = (window as any).imageViewerFabricCanvas;
  }

  if (!fabricCanvas) {
    console.error(`No fabric canvas found for ${containerId}`);
    return;
  }

  annotationState.currentTool = tool;
  annotationState.activeCanvasId = containerId;
  fabricCanvas.isDrawingMode = false;
  fabricCanvas.selection = false;

  fabricCanvas.off("mouse:down", handleMouseDown);
  fabricCanvas.off("mouse:down", handlePolygonClick);
  fabricCanvas.off("mouse:move", handleMouseMove);
  fabricCanvas.off("mouse:up", handleMouseUp);

  if (tool === "polygon" || tool === "triangle") {
    fabricCanvas.on("mouse:down", handlePolygonClick);
  } else {
    fabricCanvas.on("mouse:down", handleMouseDown);
    fabricCanvas.on("mouse:move", handleMouseMove);
    fabricCanvas.on("mouse:up", handleMouseUp);
  }

  fabricCanvas.defaultCursor = "crosshair";
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function handleMouseDown(options: any) {
  const fabricCanvas = getActiveCanvas();
  if (!fabricCanvas || !annotationState.currentTool) return;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const pointer = (fabricCanvas as any).getPointer(options.e);
  annotationState.isDrawing = true;
  annotationState.startPoint = { x: pointer.x, y: pointer.y };

  createShape(annotationState.currentTool, pointer.x, pointer.y);
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function handleMouseMove(options: any) {
  const fabricCanvas = getActiveCanvas();
  if (
    !fabricCanvas ||
    !annotationState.isDrawing ||
    !annotationState.startPoint
  )
    return;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const pointer = (fabricCanvas as any).getPointer(options.e);
  updateShape(
    annotationState.currentTool!,
    annotationState.startPoint,
    pointer
  );
}

function handleMouseUp() {
  annotationState.isDrawing = false;
  annotationState.startPoint = null;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function handlePolygonClick(options: any) {
  const fabricCanvas = getActiveCanvas();
  if (!fabricCanvas || !annotationState.currentTool) return;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const pointer = (fabricCanvas as any).getPointer(options.e);

  if (annotationState.currentTool === "polygon") {
    annotationState.polygonPoints.push({ x: pointer.x, y: pointer.y });

    if (annotationState.polygonPoints.length >= 3) {
      if ((options.e as MouseEvent).detail === 2) {
        createPolygon(annotationState.polygonPoints);
        annotationState.polygonPoints = [];
      }
    }
  } else if (annotationState.currentTool === "triangle") {
    annotationState.polygonPoints.push({ x: pointer.x, y: pointer.y });

    if (annotationState.polygonPoints.length === 3) {
      createTriangle(annotationState.polygonPoints);
      annotationState.polygonPoints = [];
    }
  }
}

function createShape(tool: AnnotationTool, x: number, y: number) {
  const fabricCanvas = getActiveCanvas();
  if (!fabricCanvas) return;

  const commonProps = {
    left: x,
    top: y,
    fill: "transparent",
    stroke: "#ff0000",
    strokeWidth: 2,
    selectable: false,
    evented: false,
  };

  let shape: fabric.Object;

  switch (tool) {
    case "rectangle":
      shape = new fabric.Rect({
        ...commonProps,
        width: 0,
        height: 0,
      });
      break;

    case "circle":
      shape = new fabric.Circle({
        ...commonProps,
        radius: 0,
      });
      break;

    case "ellipse":
      shape = new fabric.Ellipse({
        ...commonProps,
        rx: 0,
        ry: 0,
      });
      break;

    case "line":
      shape = new fabric.Line([x, y, x, y], {
        ...commonProps,
        fill: undefined,
      });
      break;

    default:
      return;
  }

  fabricCanvas.add(shape);
  fabricCanvas.renderAll();
}

function updateShape(
  tool: AnnotationTool,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
) {
  const fabricCanvas = getActiveCanvas();
  if (!fabricCanvas) return;

  const objects = fabricCanvas.getObjects();
  const lastObject = objects[objects.length - 1];

  if (!lastObject) return;

  switch (tool) {
    case "rectangle": {
      const rect = lastObject as fabric.Rect;
      const width = Math.abs(currentPoint.x - startPoint.x);
      const height = Math.abs(currentPoint.y - startPoint.y);
      rect.set({
        left: Math.min(startPoint.x, currentPoint.x),
        top: Math.min(startPoint.y, currentPoint.y),
        width,
        height,
      });
      break;
    }

    case "circle": {
      const circle = lastObject as fabric.Circle;
      const radius = Math.sqrt(
        Math.pow(currentPoint.x - startPoint.x, 2) +
          Math.pow(currentPoint.y - startPoint.y, 2)
      );
      circle.set({
        left: startPoint.x - radius,
        top: startPoint.y - radius,
        radius,
      });
      break;
    }

    case "ellipse": {
      const ellipse = lastObject as fabric.Ellipse;
      const rx = Math.abs(currentPoint.x - startPoint.x) / 2;
      const ry = Math.abs(currentPoint.y - startPoint.y) / 2;
      ellipse.set({
        left: Math.min(startPoint.x, currentPoint.x),
        top: Math.min(startPoint.y, currentPoint.y),
        rx,
        ry,
      });
      break;
    }

    case "line": {
      const line = lastObject as fabric.Line;
      line.set({
        x2: currentPoint.x,
        y2: currentPoint.y,
      });
      break;
    }
  }

  fabricCanvas.renderAll();
}

function createPolygon(points: { x: number; y: number }[]) {
  const fabricCanvas = getActiveCanvas();
  if (!fabricCanvas || points.length < 3) return;

  const polygon = new fabric.Polygon(points, {
    fill: "transparent",
    stroke: "#ff00ff",
    strokeWidth: 2,
    selectable: false,
    evented: false,
  });

  fabricCanvas.add(polygon);
  fabricCanvas.renderAll();
}

function createTriangle(points: { x: number; y: number }[]) {
  if (points.length !== 3) return;
  createPolygon(points);
}

export function clearAnnotations(containerId?: string) {
  if (containerId) {
    let fabricCanvas = canvasInstances.get(containerId);

    // For StackViewer, try to get the canvas from window
    if (!fabricCanvas && containerId === "stack-viewer-container") {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      fabricCanvas = (window as any).stackViewerFabricCanvas;
    }

    // For ImageViewer, try to get the canvas from window
    if (!fabricCanvas && containerId === "image-viewer-container") {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      fabricCanvas = (window as any).imageViewerFabricCanvas;
    }

    if (fabricCanvas) {
      fabricCanvas.clear();
    }
  } else {
    const fabricCanvas = getActiveCanvas();
    if (fabricCanvas) {
      fabricCanvas.clear();
    }
  }
  annotationState.polygonPoints = [];
}

export function disableAnnotationMode() {
  const fabricCanvas = getActiveCanvas();
  if (!fabricCanvas) return;

  annotationState.currentTool = null;
  annotationState.isDrawing = false;
  annotationState.startPoint = null;
  annotationState.polygonPoints = [];
  annotationState.activeCanvasId = null;

  fabricCanvas.off("mouse:down", handleMouseDown);
  fabricCanvas.off("mouse:down", handlePolygonClick);
  fabricCanvas.off("mouse:move", handleMouseMove);
  fabricCanvas.off("mouse:up", handleMouseUp);
  fabricCanvas.defaultCursor = "default";
}

export function removeAnnotations(containerId: string) {
  const canvas = document.getElementById(`${containerId}-annotations`);
  if (canvas) {
    // Clean up resize observer if it exists
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const resizeObserver = (canvas as any)._resizeObserver;
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
    canvas.remove();
  }
  canvasInstances.delete(containerId);
  if (annotationState.activeCanvasId === containerId) {
    annotationState.activeCanvasId = null;
  }
}
