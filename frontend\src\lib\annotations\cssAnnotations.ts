export type AnnotationTool =
  | "rectangle"
  | "circle"
  | "ellipse"
  | "line"
  | "polygon"
  | "triangle";

interface AnnotationState {
  isDrawing: boolean;
  currentTool: AnnotationTool | null;
  startX: number;
  startY: number;
  currentElement: HTMLElement | null;
  points: { x: number; y: number }[];
}

const annotationState: AnnotationState = {
  isDrawing: false,
  currentTool: null,
  startX: 0,
  startY: 0,
  currentElement: null,
  points: [],
};

let annotationContainer: HTMLElement | null = null;

export function initializeAnnotations(
  targetElement: HTMLElement,
  containerId: string
) {
  const container =
    document.getElementById(containerId) || targetElement.parentElement;
  if (!container) return null;

  const overlay = document.createElement("div");
  overlay.id = `${containerId}-annotations`;
  overlay.style.position = "absolute";
  overlay.style.top = "0";
  overlay.style.left = "0";
  overlay.style.width = "100%";
  overlay.style.height = "100%";
  overlay.style.pointerEvents = "none";
  overlay.style.zIndex = "10";

  if (
    container.style.position !== "relative" &&
    container.style.position !== "absolute"
  ) {
    container.style.position = "relative";
  }

  container.appendChild(overlay);
  annotationContainer = overlay;

  return overlay;
}

export function setAnnotationTool(
  tool: AnnotationTool,
  targetElement: HTMLElement
) {
  if (!annotationContainer) return;

  annotationState.currentTool = tool;

  targetElement.removeEventListener("mousedown", handleMouseDown);
  targetElement.removeEventListener("mousemove", handleMouseMove);
  targetElement.removeEventListener("mouseup", handleMouseUp);
  targetElement.removeEventListener("click", handleClick);

  targetElement.addEventListener("mousedown", handleMouseDown);
  targetElement.addEventListener("mousemove", handleMouseMove);
  targetElement.addEventListener("mouseup", handleMouseUp);

  if (tool === "polygon" || tool === "triangle") {
    targetElement.addEventListener("click", handleClick);
  }

  targetElement.style.cursor = "crosshair";
}

function handleMouseDown(e: MouseEvent) {
  if (!annotationState.currentTool || !annotationContainer) return;

  const rect = (e.target as HTMLElement).getBoundingClientRect();
  annotationState.startX = e.clientX - rect.left;
  annotationState.startY = e.clientY - rect.top;
  annotationState.isDrawing = true;

  if (
    ["rectangle", "circle", "ellipse", "line"].includes(
      annotationState.currentTool
    )
  ) {
    createShape(
      annotationState.currentTool,
      annotationState.startX,
      annotationState.startY
    );
  }
}

function handleMouseMove(e: MouseEvent) {
  if (!annotationState.isDrawing || !annotationState.currentElement) return;

  const rect = (e.target as HTMLElement).getBoundingClientRect();
  const currentX = e.clientX - rect.left;
  const currentY = e.clientY - rect.top;

  updateShape(
    annotationState.currentTool!,
    annotationState.startX,
    annotationState.startY,
    currentX,
    currentY
  );
}

function handleMouseUp() {
  annotationState.isDrawing = false;
  annotationState.currentElement = null;
}

function handleClick(e: MouseEvent) {
  if (!annotationState.currentTool || !annotationContainer) return;

  const rect = (e.target as HTMLElement).getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  if (annotationState.currentTool === "polygon") {
    annotationState.points.push({ x, y });

    if (annotationState.points.length >= 3) {
      if (e.detail === 2) {
        createPolygon(annotationState.points);
        annotationState.points = [];
      }
    }
  } else if (annotationState.currentTool === "triangle") {
    annotationState.points.push({ x, y });

    if (annotationState.points.length === 3) {
      createTriangle(annotationState.points);
      annotationState.points = [];
    }
  }
}

function createShape(tool: AnnotationTool, startX: number, startY: number) {
  if (!annotationContainer) return;

  const element = document.createElement("div");
  element.className = `annotation annotation-${tool}`;
  element.style.position = "absolute";
  element.style.border = "2px solid #ff0000";
  element.style.backgroundColor = "transparent";
  element.style.pointerEvents = "none";

  switch (tool) {
    case "rectangle":
      element.style.left = `${startX}px`;
      element.style.top = `${startY}px`;
      element.style.width = "0px";
      element.style.height = "0px";
      break;

    case "circle":
    case "ellipse":
      element.style.borderRadius = tool === "circle" ? "50%" : "50%";
      element.style.left = `${startX}px`;
      element.style.top = `${startY}px`;
      element.style.width = "0px";
      element.style.height = "0px";
      break;

    case "line":
      element.style.height = "2px";
      element.style.backgroundColor = "#ff0000";
      element.style.border = "none";
      element.style.left = `${startX}px`;
      element.style.top = `${startY}px`;
      element.style.width = "0px";
      element.style.transformOrigin = "left center";
      break;
  }

  annotationContainer.appendChild(element);
  annotationState.currentElement = element;
}

function updateShape(
  tool: AnnotationTool,
  startX: number,
  startY: number,
  currentX: number,
  currentY: number
) {
  if (!annotationState.currentElement) return;

  const element = annotationState.currentElement;

  switch (tool) {
    case "rectangle":
      const width = Math.abs(currentX - startX);
      const height = Math.abs(currentY - startY);
      const left = Math.min(startX, currentX);
      const top = Math.min(startY, currentY);

      element.style.left = `${left}px`;
      element.style.top = `${top}px`;
      element.style.width = `${width}px`;
      element.style.height = `${height}px`;
      break;

    case "circle":
      const radius = Math.sqrt(
        Math.pow(currentX - startX, 2) + Math.pow(currentY - startY, 2)
      );
      element.style.left = `${startX - radius}px`;
      element.style.top = `${startY - radius}px`;
      element.style.width = `${radius * 2}px`;
      element.style.height = `${radius * 2}px`;
      break;

    case "ellipse":
      const ellipseWidth = Math.abs(currentX - startX);
      const ellipseHeight = Math.abs(currentY - startY);
      const ellipseLeft = Math.min(startX, currentX);
      const ellipseTop = Math.min(startY, currentY);

      element.style.left = `${ellipseLeft}px`;
      element.style.top = `${ellipseTop}px`;
      element.style.width = `${ellipseWidth}px`;
      element.style.height = `${ellipseHeight}px`;
      break;

    case "line":
      const length = Math.sqrt(
        Math.pow(currentX - startX, 2) + Math.pow(currentY - startY, 2)
      );
      const angle =
        (Math.atan2(currentY - startY, currentX - startX) * 180) / Math.PI;

      element.style.width = `${length}px`;
      element.style.transform = `rotate(${angle}deg)`;
      break;
  }
}

function createPolygon(points: { x: number; y: number }[]) {
  if (!annotationContainer || points.length < 3) return;

  const element = document.createElement("div");
  element.className = "annotation annotation-polygon";
  element.style.position = "absolute";
  element.style.border = "2px solid #ff00ff";
  element.style.backgroundColor = "transparent";
  element.style.pointerEvents = "none";

  const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  const polygon = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "polygon"
  );

  const pointsStr = points.map((p) => `${p.x},${p.y}`).join(" ");
  polygon.setAttribute("points", pointsStr);
  polygon.setAttribute("fill", "transparent");
  polygon.setAttribute("stroke", "#ff00ff");
  polygon.setAttribute("stroke-width", "2");

  svg.appendChild(polygon);
  element.appendChild(svg);

  const minX = Math.min(...points.map((p) => p.x));
  const minY = Math.min(...points.map((p) => p.y));
  const maxX = Math.max(...points.map((p) => p.x));
  const maxY = Math.max(...points.map((p) => p.y));

  element.style.left = `${minX}px`;
  element.style.top = `${minY}px`;
  element.style.width = `${maxX - minX}px`;
  element.style.height = `${maxY - minY}px`;

  svg.style.width = "100%";
  svg.style.height = "100%";
  svg.setAttribute("viewBox", `${minX} ${minY} ${maxX - minX} ${maxY - minY}`);

  annotationContainer.appendChild(element);
}

function createTriangle(points: { x: number; y: number }[]) {
  if (!annotationContainer || points.length !== 3) return;

  createPolygon(points);
}

export function clearAnnotations() {
  if (!annotationContainer) return;

  annotationContainer.innerHTML = "";
  annotationState.points = [];
}

export function disableAnnotationMode(targetElement: HTMLElement) {
  annotationState.currentTool = null;
  annotationState.isDrawing = false;
  annotationState.currentElement = null;
  annotationState.points = [];

  targetElement.removeEventListener("mousedown", handleMouseDown);
  targetElement.removeEventListener("mousemove", handleMouseMove);
  targetElement.removeEventListener("mouseup", handleMouseUp);
  targetElement.removeEventListener("click", handleClick);

  targetElement.style.cursor = "default";
}

export function removeAnnotations(containerId: string) {
  const overlay = document.getElementById(`${containerId}-annotations`);
  if (overlay) {
    overlay.remove();
  }
  annotationContainer = null;
}
