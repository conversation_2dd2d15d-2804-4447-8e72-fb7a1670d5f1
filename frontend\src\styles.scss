* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  color: #2c3e50;
}


.home-container {
  padding: 50px 20px;
  text-align: center;
}

.home-title {
  margin-bottom: 20px;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.8px;
  color: #4a5568;
  line-height: 1.2;
}

.home-description {
  margin-bottom: 30px;
  color: #4a5568;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.5;
}

.home-button {
  padding: 14px 28px;
  background: #007acc;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: none;

  &:hover {
    background: #005a9e;
  }
}

.home-button-link {
  text-decoration: none;
  color: inherit;
}


.stack-viewer {
  height: 100vh;
  background: #000;
  color: white;
  position: relative;

  .controls {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    padding: 15px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    width: 250px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

.volume-viewer {
  height: 100vh;
  background: #000;
  color: white;
  position: relative;

  .controls {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    min-width: 200px;
  }

  .mode-switcher {
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 12px;
    color: white;
    cursor: pointer;

    &-item {
      &.active {
        color: #4CAF50;
      }

      &.inactive {
        color: #888;
      }
    }

    &-separator {
      margin: 0 5px;
    }
  }
}

.image-viewer {
  width: 100%;
  height: 100vh;
  background: #000;
  color: white;
  position: relative;
  overflow: hidden;

  .controls {
    position: absolute;
    top: 20px;
    right: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    width: 250px;
    z-index: 10;
  }

  #image-canvas {
    background: #333;
    position: absolute;
  }
}


.patient-viewer {
  display: flex;
  height: 100vh;

  .file-list {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
    flex-shrink: 0;
    border-right: 1px solid #ccc;

    .patient-header {
      padding: 15px;
      border-bottom: 1px solid #ccc;
      background: #d0d0d0;

      h3 {
        margin: 0 0 8px 0;
        color: #1a202c;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: -0.4px;
        line-height: 1.3;
      }

      .patient-details {
        font-size: 14px;
        color: #4a5568;
        font-weight: 500;
        line-height: 1.4;
        letter-spacing: 0.1px;
      }
    }

    .file-list-header {
      padding: 10px 15px;
      border-bottom: 1px solid #ccc;

      h4 {
        margin: 0;
        color: #2d3748;
        font-size: 15px;
        font-weight: 700;
        letter-spacing: 0.2px;
        text-transform: uppercase;
      }

      .file-count {
        font-size: 13px;
        color: #4a5568;
        font-weight: 500;
        letter-spacing: 0.1px;
      }
    }

    .file-items {
      .file-item {
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 1px solid #ccc;

        &:hover {
          background: #d5d5d5;
        }

        &.selected {
          background: #007acc;
          color: white;

          .file-name {
            color: white;
          }
        }

        .file-name {
          font-weight: 600;
          margin-bottom: 4px;
          font-size: 15px;
          letter-spacing: -0.2px;
          color: #2d3748;
          line-height: 1.3;
        }

        .file-type {
          font-size: 11px;
          color: #718096;
          text-transform: uppercase;
          font-weight: 600;
          letter-spacing: 0.8px;
        }
      }
    }
  }

  .viewer-panel {
    flex: 1;
    min-width: 0;
    background: white;
    position: relative;

    .empty-state,
    .loading-state,
    .error-state {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #666;
    }
  }
}


.controls {
  .control-group {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 8px;
      font-size: 13px;
      font-weight: 600;
      letter-spacing: 0.25px;
      color: #fff;
      text-transform: uppercase;
    }

    input[type="range"] {
      width: 100%;
      margin-bottom: 5px;
      accent-color: #007acc;
    }

    span {
      font-size: 12px;
      color: #ccc;
      font-weight: 500;
      display: block;
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      margin-top: 5px;
    }
  }

  .tools-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    margin-bottom: 10px;
  }

  .tool-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 6px;
    font-size: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    text-align: center;
    min-height: 36px;

    svg {
      font-size: 12px;
      flex-shrink: 0;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      background: rgba(0, 122, 204, 0.3);
    }
  }

  .save-button {
    background: #007acc;
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    margin-top: 10px;
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
    width: 100%;

    &:hover {
      background: #005a9e;
    }
  }
}




.fabric-toolbar-vertical {
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
  width: 70px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  padding: 8px 6px;

  .toolbar-header {
    margin-bottom: 8px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 6px;

    h5 {
      margin: 0;
      color: #fff;
      font-size: 10px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .control-item {
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;

    label {
      font-size: 8px;
      font-weight: 600;
      letter-spacing: 0.3px;
      color: #fff;
      text-transform: uppercase;
      margin-bottom: 3px;
      text-align: center;
      line-height: 1.2;
    }

    .horizontal-slider {
      -webkit-appearance: none;
      width: 50px;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      outline: none;
      border-radius: 2px;
      margin: 2px 0;
      accent-color: #007acc;

      &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007acc;
        cursor: pointer;
        border: 1px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }

      &::-moz-range-thumb {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007acc;
        cursor: pointer;
        border: 1px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }
    }

    span {
      font-size: 8px;
      color: #ccc;
      font-weight: 500;
      background: rgba(255, 255, 255, 0.1);
      padding: 1px 3px;
      border-radius: 2px;
      margin-top: 2px;
      min-width: 20px;
      text-align: center;
    }
  }

  .save-btn {
    background: #007acc;
    color: white;
    border: none;
    padding: 6px 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 9px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
    width: 100%;
    border-radius: 4px;
    margin-top: 6px;

    &:hover {
      background: #005a9e;
      transform: translateY(-1px);
    }
  }

  .annotation-tools {
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);

    label {
      font-size: 8px;
      font-weight: 600;
      letter-spacing: 0.3px;
      color: #fff;
      text-transform: uppercase;
      margin-bottom: 6px;
      text-align: center;
      display: block;
    }

    .tool-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3px;
      margin-bottom: 6px;
    }

    .tool-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #fff;
      padding: 6px 3px;
      cursor: pointer;
      border-radius: 3px;
      font-size: 12px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 24px;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-1px);
      }

      &.active {
        background: #007acc;
        border-color: #007acc;
        color: white;
      }
    }

    .clear-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 4px 6px;
      cursor: pointer;
      font-weight: 600;
      font-size: 8px;
      text-transform: uppercase;
      letter-spacing: 0.3px;
      transition: all 0.2s ease;
      width: 100%;
      border-radius: 3px;

      &:hover {
        background: #c82333;
        transform: translateY(-1px);
      }
    }
  }
}


.annotation {
  position: absolute;
  pointer-events: none;
  z-index: 10;

  &.annotation-rectangle {
    border: 2px solid #ff0000;
    background: transparent;
  }

  &.annotation-circle {
    border: 2px solid #00ff00;
    background: transparent;
    border-radius: 50%;
  }

  &.annotation-ellipse {
    border: 2px solid #0000ff;
    background: transparent;
    border-radius: 50%;
  }

  &.annotation-line {
    background: #ffff00;
    height: 2px;
    border: none;
    transform-origin: left center;
  }

  &.annotation-polygon {
    border: none;
    background: transparent;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &.annotation-triangle {
    border: none;
    background: transparent;

    svg {
      width: 100%;
      height: 100%;
    }
  }
}