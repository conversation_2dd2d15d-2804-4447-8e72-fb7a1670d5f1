import React, { useState, useEffect } from "react";
import {
  setAnnotationTool,
  clearAnnotations,
  disableAnnotationMode,
  type AnnotationTool,
} from "@/lib/fabric";
import type { FabricToolbarProps } from "@/shared/types";

export default function FabricToolbar({
  initialValues,
  onSave,
  cssFilterMode,
  enableAnnotations = false,
  containerId,
}: FabricToolbarProps) {
  const [brightness, setBrightness] = useState(
    initialValues.brightness || 1
  );
  const [contrast, setContrast] = useState(initialValues.contrast || 1);
  const [saturation, setSaturation] = useState(
    initialValues.saturation || 1
  );
  const [rotation, setRotation] = useState(initialValues.rotation || 0);
  const [activeTool, setActiveTool] = useState<AnnotationTool | null>(null);

  useEffect(() => {
    setBrightness(initialValues.brightness || 1);
    setContrast(initialValues.contrast || 1);
    setSaturation(initialValues.saturation || 1);
    setRotation(initialValues.rotation || 0);
  }, [initialValues]);

  const handleBrightnessChange = (value: number) => {
    setBrightness(value);

    if (cssFilterMode) {
      // Use CSS filters for both ImageViewer and StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        const currentFilter = element.style.filter;
        const newFilter =
          currentFilter.replace(/brightness\([^)]*\)/, "") +
          ` brightness(${value})`;
        element.style.filter = newFilter.trim();
      }
    }
  };

  const handleContrastChange = (value: number) => {
    setContrast(value);

    if (cssFilterMode) {
      // Use CSS filters for both ImageViewer and StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        const currentFilter = element.style.filter;
        const newFilter =
          currentFilter.replace(/contrast\([^)]*\)/, "") +
          ` contrast(${value})`;
        element.style.filter = newFilter.trim();
      }
    }
  };

  const handleSaturationChange = (value: number) => {
    setSaturation(value);

    if (cssFilterMode) {
      // Use CSS filters for both ImageViewer and StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        const currentFilter = element.style.filter;
        const newFilter =
          currentFilter.replace(/saturate\([^)]*\)/, "") +
          ` saturate(${value})`;
        element.style.filter = newFilter.trim();
      }
    }
  };

  const handleRotationChange = (value: number) => {
    setRotation(value);

    if (cssFilterMode) {
      // Use CSS transform for both ImageViewer and StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        element.style.transform = `rotate(${value}deg)`;
      }
    }
  };

  const handleSave = () => {
    if (onSave) {
      onSave({
        brightness,
        contrast,
        saturation,
        rotation,
      });
    }
  };

  const handleToolSelect = (tool: AnnotationTool) => {
    if (!containerId) return;

    if (activeTool === tool) {
      // Deselect current tool
      setActiveTool(null);
      disableAnnotationMode();
    } else {
      // Select new tool
      setActiveTool(tool);
      setAnnotationTool(tool, containerId);
    }
  };

  const handleClearAnnotations = () => {
    if (containerId) {
      clearAnnotations(containerId);
    }
  };

  const annotationTools: { tool: AnnotationTool; icon: string; label: string }[] = [
    { tool: "rectangle", icon: "⬜", label: "Rect" },
    { tool: "circle", icon: "⭕", label: "Circle" },
    { tool: "ellipse", icon: "⭕", label: "Ellipse" },
    { tool: "line", icon: "📏", label: "Line" },
    { tool: "polygon", icon: "🔷", label: "Polygon" },
    { tool: "triangle", icon: "🔺", label: "Triangle" },
  ];

  return (
    <div className="fabric-toolbar-vertical">
      <div className="toolbar-header">
        <h5>Fabric Controls</h5>
      </div>

      <div className="control-item">
        <label>Brightness</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={brightness}
          onChange={(e) => handleBrightnessChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{brightness.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label>Contrast</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={contrast}
          onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{contrast.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label>Saturation</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={saturation}
          onChange={(e) => handleSaturationChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{saturation.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label>Rotation</label>
        <input
          type="range"
          min="0"
          max="360"
          step="15"
          value={rotation}
          onChange={(e) => handleRotationChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{rotation}°</span>
      </div>

      <button className="save-btn" onClick={handleSave}>
        Save
      </button>

      {enableAnnotations && (
        <div className="annotation-tools">
          <label>Annotations</label>
          <div className="tool-grid">
            {annotationTools.map(({ tool, icon, label }) => (
              <button
                key={tool}
                className={`tool-btn ${activeTool === tool ? "active" : ""}`}
                onClick={() => handleToolSelect(tool)}
                title={label}
              >
                {icon}
              </button>
            ))}
          </div>
          <button className="clear-btn" onClick={handleClearAnnotations}>
            Clear
          </button>
        </div>
      )}
    </div>
  );
}
