import { fabric } from "fabric";

export type AnnotationTool =
  | "rectangle"
  | "circle"
  | "ellipse"
  | "line"
  | "polygon"
  | "triangle";

interface AnnotationState {
  currentTool: AnnotationTool | null;
  isDrawing: boolean;
  startPoint: { x: number; y: number } | null;
  polygonPoints: { x: number; y: number }[];
}

const annotationState: AnnotationState = {
  currentTool: null,
  isDrawing: false,
  startPoint: null,
  polygonPoints: [],
};

let fabricCanvas: fabric.Canvas | null = null;

export function initializeAnnotations(
  targetElement: HTMLElement,
  containerId: string
) {
  const container =
    document.getElementById(containerId) || targetElement.parentElement;
  if (!container) return null;

  const canvas = document.createElement("canvas");
  canvas.id = `${containerId}-annotations`;
  canvas.style.position = "absolute";
  canvas.style.top = "0";
  canvas.style.left = "0";
  canvas.style.pointerEvents = "auto";
  canvas.style.zIndex = "10";

  if (
    container.style.position !== "relative" &&
    container.style.position !== "absolute"
  ) {
    container.style.position = "relative";
  }

  container.appendChild(canvas);

  fabricCanvas = new fabric.Canvas(canvas, {
    selection: false,
    isDrawingMode: false,
  });

  const resizeCanvas = () => {
    const rect = container.getBoundingClientRect();
    fabricCanvas?.setDimensions({
      width: rect.width,
      height: rect.height,
    });
  };

  resizeCanvas();
  window.addEventListener("resize", resizeCanvas);

  return canvas;
}

export function setAnnotationTool(
  tool: AnnotationTool,
  targetElement: HTMLElement
) {
  if (!fabricCanvas) return;

  annotationState.currentTool = tool;
  fabricCanvas.isDrawingMode = false;
  fabricCanvas.selection = false;

  fabricCanvas.off("mouse:down");
  fabricCanvas.off("mouse:move");
  fabricCanvas.off("mouse:up");

  if (tool === "polygon" || tool === "triangle") {
    fabricCanvas.on("mouse:down", handlePolygonClick);
  } else {
    fabricCanvas.on("mouse:down", handleMouseDown);
    fabricCanvas.on("mouse:move", handleMouseMove);
    fabricCanvas.on("mouse:up", handleMouseUp);
  }

  fabricCanvas.defaultCursor = "crosshair";
}

function handleMouseDown(options: fabric.IEvent) {
  if (!fabricCanvas || !annotationState.currentTool) return;

  const pointer = fabricCanvas.getPointer(options.e);
  annotationState.isDrawing = true;
  annotationState.startPoint = { x: pointer.x, y: pointer.y };

  createShape(annotationState.currentTool, pointer.x, pointer.y);
}

function handleMouseMove(options: fabric.IEvent) {
  if (!fabricCanvas || !annotationState.isDrawing || !annotationState.startPoint) return;

  const pointer = fabricCanvas.getPointer(options.e);
  updateShape(annotationState.currentTool!, annotationState.startPoint, pointer);
}

function handleMouseUp() {
  annotationState.isDrawing = false;
  annotationState.startPoint = null;
}

function handlePolygonClick(options: fabric.IEvent) {
  if (!fabricCanvas || !annotationState.currentTool) return;

  const pointer = fabricCanvas.getPointer(options.e);

  if (annotationState.currentTool === "polygon") {
    annotationState.polygonPoints.push({ x: pointer.x, y: pointer.y });

    if (annotationState.polygonPoints.length >= 3) {
      if ((options.e as MouseEvent).detail === 2) {
        createPolygon(annotationState.polygonPoints);
        annotationState.polygonPoints = [];
      }
    }
  } else if (annotationState.currentTool === "triangle") {
    annotationState.polygonPoints.push({ x: pointer.x, y: pointer.y });

    if (annotationState.polygonPoints.length === 3) {
      createTriangle(annotationState.polygonPoints);
      annotationState.polygonPoints = [];
    }
  }
}

function createShape(tool: AnnotationTool, x: number, y: number) {
  if (!fabricCanvas) return;

  const commonProps = {
    left: x,
    top: y,
    fill: "transparent",
    stroke: "#ff0000",
    strokeWidth: 2,
    selectable: false,
    evented: false,
  };

  let shape: fabric.Object;

  switch (tool) {
    case "rectangle":
      shape = new fabric.Rect({
        ...commonProps,
        width: 0,
        height: 0,
      });
      break;

    case "circle":
      shape = new fabric.Circle({
        ...commonProps,
        radius: 0,
      });
      break;

    case "ellipse":
      shape = new fabric.Ellipse({
        ...commonProps,
        rx: 0,
        ry: 0,
      });
      break;

    case "line":
      shape = new fabric.Line([x, y, x, y], {
        ...commonProps,
        fill: undefined,
      });
      break;

    default:
      return;
  }

  fabricCanvas.add(shape);
  fabricCanvas.renderAll();
}

function updateShape(
  tool: AnnotationTool,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
) {
  if (!fabricCanvas) return;

  const objects = fabricCanvas.getObjects();
  const lastObject = objects[objects.length - 1];

  if (!lastObject) return;

  switch (tool) {
    case "rectangle":
      const rect = lastObject as fabric.Rect;
      const width = Math.abs(currentPoint.x - startPoint.x);
      const height = Math.abs(currentPoint.y - startPoint.y);
      rect.set({
        left: Math.min(startPoint.x, currentPoint.x),
        top: Math.min(startPoint.y, currentPoint.y),
        width,
        height,
      });
      break;

    case "circle":
      const circle = lastObject as fabric.Circle;
      const radius = Math.sqrt(
        Math.pow(currentPoint.x - startPoint.x, 2) +
        Math.pow(currentPoint.y - startPoint.y, 2)
      );
      circle.set({
        left: startPoint.x - radius,
        top: startPoint.y - radius,
        radius,
      });
      break;

    case "ellipse":
      const ellipse = lastObject as fabric.Ellipse;
      const rx = Math.abs(currentPoint.x - startPoint.x) / 2;
      const ry = Math.abs(currentPoint.y - startPoint.y) / 2;
      ellipse.set({
        left: Math.min(startPoint.x, currentPoint.x),
        top: Math.min(startPoint.y, currentPoint.y),
        rx,
        ry,
      });
      break;

    case "line":
      const line = lastObject as fabric.Line;
      line.set({
        x2: currentPoint.x,
        y2: currentPoint.y,
      });
      break;
  }

  fabricCanvas.renderAll();
}

function createPolygon(points: { x: number; y: number }[]) {
  if (!fabricCanvas || points.length < 3) return;

  const polygon = new fabric.Polygon(points, {
    fill: "transparent",
    stroke: "#ff00ff",
    strokeWidth: 2,
    selectable: false,
    evented: false,
  });

  fabricCanvas.add(polygon);
  fabricCanvas.renderAll();
}

function createTriangle(points: { x: number; y: number }[]) {
  if (!fabricCanvas || points.length !== 3) return;
  createPolygon(points);
}

export function clearAnnotations() {
  if (!fabricCanvas) return;
  fabricCanvas.clear();
  annotationState.polygonPoints = [];
}

export function disableAnnotationMode(targetElement: HTMLElement) {
  if (!fabricCanvas) return;

  annotationState.currentTool = null;
  annotationState.isDrawing = false;
  annotationState.startPoint = null;
  annotationState.polygonPoints = [];

  fabricCanvas.off("mouse:down");
  fabricCanvas.off("mouse:move");
  fabricCanvas.off("mouse:up");
  fabricCanvas.defaultCursor = "default";
}

export function removeAnnotations(containerId: string) {
  const canvas = document.getElementById(`${containerId}-annotations`);
  if (canvas) {
    canvas.remove();
  }
  fabricCanvas = null;
}
