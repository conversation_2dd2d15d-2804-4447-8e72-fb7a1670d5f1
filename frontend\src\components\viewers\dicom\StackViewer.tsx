import { useRef, useEffect, useState } from "react";
import { Types, cache } from "@cornerstonejs/core";
import { ToolGroupManager, annotation } from "@cornerstonejs/tools";
import {
  initializeCornerstone,
  createRenderingEngine,
  setup2dViewport,
  loadDicomStack,
  adjustBrightness,
  adjustContrast,
  setupViewer,
  stackViewerConfig,
  setPrimaryTool,
  toolConfig,
  getViewportAnnotations,
  restoreViewportAnnotations,
} from "@/lib/dicom";

import { removeAnnotations } from "@/lib/fabric";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import { saveStackConfig } from "@/shared/api";
import { dicomConstants } from "@/shared/constants";
import * as fabric from "fabric";
import { DicomStackViewerProps } from "@/shared/types";

const { renderingEngineId, viewportId, toolGroupId } = dicomConstants;

export default function StackViewer({ data }: DicomStackViewerProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const dicomFileRef = useRef<string>("");
  const cornerstoneCanvasRef = useRef<HTMLCanvasElement | null>(null);

  const [isInitialized, setIsInitialized] = useState(false);
  const [contrast, setContrast] = useState(data.viewer.configs.contrast);
  const [brightness, setBrightness] = useState(data.viewer.configs.brightness);

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);
    return () => {
      annotation.state.removeAllAnnotations();
      ToolGroupManager.destroyToolGroup(toolGroupId);
      renderingEngineRef.current?.destroy();
      cache.purgeCache();

      if (cornerstoneCanvasRef.current) {
        cornerstoneCanvasRef.current.style.filter = "";
        cornerstoneCanvasRef.current.style.transform = "";
      }

      removeAnnotations("stack-viewer-container");
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !elementRef.current) return;

    const initializeViewer = async () => {
      const element = elementRef.current;
      if (!element) return;

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;
      const viewport = setup2dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;

      setupViewer(
        toolGroupId,
        viewportId,
        renderingEngineId,
        stackViewerConfig
      );

      const imageUrl = data.viewer.imageUrl;
      dicomFileRef.current = imageUrl;

      await loadDicomStack(viewport, imageUrl);
      adjustContrast(viewport, contrast);
      adjustBrightness(viewport, brightness);

      if (data.viewer.configs.annotations) {
        restoreViewportAnnotations(
          data.viewer.configs.annotations,
          viewportId,
          viewport
        );
      }

      const cornerstoneCanvas = viewport.getCanvas();
      if (cornerstoneCanvas) {
        cornerstoneCanvasRef.current = cornerstoneCanvas;
        console.log(
          "🎯 Cornerstone canvas found, scheduling Fabric overlay initialization"
        );

        // Initialize Fabric.js overlay after DICOM is loaded
        setTimeout(() => {
          console.log("⏰ Timeout reached, calling initializeFabricOverlay");
          initializeFabricOverlay(cornerstoneCanvas);
        }, 500);
      } else {
        console.error("❌ No Cornerstone canvas found");
      }
    };

    initializeViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, data]);

  const handleContrastChange = (value: number) => {
    if (!viewportRef.current) return;
    setContrast(value);
    adjustContrast(viewportRef.current, value);
  };
  const handleBrigtnessChange = (value: number) => {
    if (!viewportRef.current) return;
    setBrightness(value);
    adjustBrightness(viewportRef.current, value);
  };

  const handleSave = async () => {
    const annotations = getViewportAnnotations(viewportId);
    await saveStackConfig(data.id, {
      contrast,
      brightness,
      annotations,
    });
  };

  const handleToolSelect = (toolName: string) => {
    setPrimaryTool(toolName, viewportId);
  };

  const initializeFabricOverlay = (cornerstoneCanvas: HTMLCanvasElement) => {
    console.log("🔧 Initializing Fabric overlay for StackViewer");

    // Create annotation canvas that overlays the DICOM canvas
    const container = document.getElementById("stack-viewer-container");
    if (!container) {
      console.error("❌ Container not found: stack-viewer-container");
      return;
    }
    console.log("✅ Container found:", container);

    // Remove existing annotation canvas
    const existingCanvas = document.getElementById("stack-viewer-annotations");
    if (existingCanvas) {
      console.log("🗑️ Removing existing canvas");
      existingCanvas.remove();
    }

    // Create new annotation canvas
    const annotationCanvas = document.createElement("canvas");
    annotationCanvas.id = "stack-viewer-annotations";
    annotationCanvas.style.position = "absolute";
    annotationCanvas.style.left = "0";
    annotationCanvas.style.top = "0";
    annotationCanvas.style.zIndex = "1000";
    annotationCanvas.style.pointerEvents = "auto";
    annotationCanvas.style.backgroundColor = "transparent";
    annotationCanvas.style.border = "2px solid blue"; // Debug border

    // Get DICOM canvas dimensions
    const dicomRect = cornerstoneCanvas.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    console.log("📐 DICOM canvas rect:", dicomRect);
    console.log("📐 Container rect:", containerRect);

    // Position annotation canvas exactly over DICOM canvas
    annotationCanvas.style.left = `${dicomRect.left - containerRect.left}px`;
    annotationCanvas.style.top = `${dicomRect.top - containerRect.top}px`;
    annotationCanvas.style.width = `${dicomRect.width}px`;
    annotationCanvas.style.height = `${dicomRect.height}px`;

    // Set canvas dimensions
    annotationCanvas.width = dicomRect.width;
    annotationCanvas.height = dicomRect.height;

    console.log("📏 Annotation canvas dimensions:", {
      width: annotationCanvas.width,
      height: annotationCanvas.height,
      style: {
        left: annotationCanvas.style.left,
        top: annotationCanvas.style.top,
        width: annotationCanvas.style.width,
        height: annotationCanvas.style.height,
      },
    });

    container.appendChild(annotationCanvas);
    console.log("✅ Annotation canvas added to container");

    // Initialize Fabric.js on the annotation canvas
    try {
      const fabricCanvas = new fabric.Canvas("stack-viewer-annotations", {
        backgroundColor: "transparent",
        selection: false,
      });
      console.log("✅ Fabric.js canvas created:", fabricCanvas);

      // Store reference for the FabricToolbar to use
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).stackViewerFabricCanvas = fabricCanvas;
      console.log("✅ Fabric canvas stored in window");

      // Add a test rectangle to verify the canvas is working
      const testRect = new fabric.Rect({
        left: 50,
        top: 50,
        width: 100,
        height: 80,
        fill: "rgba(0,0,0,0)",
        stroke: "red",
        strokeWidth: 2,
        selectable: false,
        evented: false,
      });
      fabricCanvas.add(testRect);
      console.log("✅ Test rectangle added to canvas");

      fabricCanvas.renderAll();
      console.log("✅ Canvas rendered");
    } catch (error) {
      console.error("❌ Error creating Fabric.js canvas:", error);
    }

    // Sync canvas size when DICOM viewport changes
    const syncCanvasSize = () => {
      const newDicomRect = cornerstoneCanvas.getBoundingClientRect();
      const newContainerRect = container.getBoundingClientRect();

      annotationCanvas.style.left = `${
        newDicomRect.left - newContainerRect.left
      }px`;
      annotationCanvas.style.top = `${
        newDicomRect.top - newContainerRect.top
      }px`;
      annotationCanvas.style.width = `${newDicomRect.width}px`;
      annotationCanvas.style.height = `${newDicomRect.height}px`;

      annotationCanvas.width = newDicomRect.width;
      annotationCanvas.height = newDicomRect.height;

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const fabricCanvas = (window as any).stackViewerFabricCanvas;
      if (fabricCanvas) {
        fabricCanvas.setDimensions({
          width: newDicomRect.width,
          height: newDicomRect.height,
        });

        fabricCanvas.renderAll();
      }
    };

    // Listen for Cornerstone events
    elementRef.current?.addEventListener(
      "cornerstoneimagerendered",
      syncCanvasSize
    );
    window.addEventListener("resize", syncCanvasSize);
  };

  return (
    <div className="stack-viewer" id="stack-viewer-container">
      <div ref={elementRef} style={{ width: "100%", height: "100%" }} />

      <div className="controls">
        <div className="control-group">
          <label>Tools:</label>
          <div className="tools-grid">
            {toolConfig.map((tool) => {
              const IconComponent = tool.icon;
              return (
                <button
                  key={tool.name}
                  onClick={() => handleToolSelect(tool.name)}
                  className="tool-button"
                  title={tool.displayName}
                >
                  <IconComponent />
                  {tool.displayName}
                </button>
              );
            })}
          </div>
        </div>

        <div className="control-group">
          <label>Contrast:</label>
          <input
            type="range"
            min={-127}
            max={127}
            step={1}
            value={contrast}
            onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
          />
          <span>{contrast.toFixed(1)}</span>
        </div>
        <div className="control-group">
          <label>Brightness:</label>
          <input
            type="range"
            min={-127}
            max={127}
            step={1}
            value={brightness}
            onChange={(e) => handleBrigtnessChange(parseFloat(e.target.value))}
          />
          <span>{brightness.toFixed(1)}</span>
        </div>

        <button className="save-button" onClick={handleSave}>
          Save Settings
        </button>
      </div>

      <FabricToolbar
        initialValues={{
          brightness: 1,
          contrast: 1,
          saturation: 1,
          rotation: 0,
        }}
        cssFilterMode={{
          targetElement: () => cornerstoneCanvasRef.current,
        }}
        enableAnnotations={true}
        containerId="stack-viewer-container"
      />
    </div>
  );
}
