import{r as pt,j as G,b as kn}from"./index-DnrQzv0z.js";import"./cornerstone-BZQd-O7z.js";import"./dicom-9ry7jN7t.js";function p(a,t,e){return(t=function(s){var r=function(i,n){if(typeof i!="object"||!i)return i;var o=i[Symbol.toPrimitive];if(o!==void 0){var h=o.call(i,n);if(typeof h!="object")return h;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(i)}(s,"string");return typeof r=="symbol"?r:r+""}(t))in a?Object.defineProperty(a,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[t]=e,a}function Ir(a,t){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(a);t&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable})),e.push.apply(e,s)}return e}function m(a){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Ir(Object(e),!0).forEach(function(s){p(a,s,e[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):Ir(Object(e)).forEach(function(s){Object.defineProperty(a,s,Object.getOwnPropertyDescriptor(e,s))})}return a}function I(a,t){if(a==null)return{};var e,s,r=function(n,o){if(n==null)return{};var h={};for(var l in n)if({}.hasOwnProperty.call(n,l)){if(o.indexOf(l)>=0)continue;h[l]=n[l]}return h}(a,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(a);for(s=0;s<i.length;s++)e=i[s],t.indexOf(e)>=0||{}.propertyIsEnumerable.call(a,e)&&(r[e]=a[e])}return r}function Ut(a,t){return t||(t=a.slice(0)),Object.freeze(Object.defineProperties(a,{raw:{value:Object.freeze(t)}}))}class Xr{constructor(){p(this,"browserShadowBlurConstant",1),p(this,"DPI",96),p(this,"devicePixelRatio",typeof window<"u"?window.devicePixelRatio:1),p(this,"perfLimitSizeTotal",2097152),p(this,"maxCacheSideLimit",4096),p(this,"minCacheSideLimit",256),p(this,"disableStyleCopyPaste",!1),p(this,"enableGLFiltering",!0),p(this,"textureSize",4096),p(this,"forceGLPutImageData",!1),p(this,"cachesBoundsOfCurve",!1),p(this,"fontPaths",{}),p(this,"NUM_FRACTION_DIGITS",4)}}const E=new class extends Xr{constructor(a){super(),this.configure(a)}configure(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Object.assign(this,a)}addFonts(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.fontPaths=m(m({},this.fontPaths),a)}removeFonts(){(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).forEach(a=>{delete this.fontPaths[a]})}clearFonts(){this.fontPaths={}}restoreDefaults(a){const t=new Xr,e=(a==null?void 0:a.reduce((s,r)=>(s[r]=t[r],s),{}))||t;this.configure(e)}},Gt=function(a){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];return console[a]("fabric",...e)};class Tt extends Error{constructor(t,e){super("fabric: ".concat(t),e)}}class Mn extends Tt{constructor(t){super("".concat(t," 'options.signal' is in 'aborted' state"))}}class En{}class jn extends En{testPrecision(t,e){const s="precision ".concat(e,` float;
void main(){}`),r=t.createShader(t.FRAGMENT_SHADER);return!!r&&(t.shaderSource(r,s),t.compileShader(r),!!t.getShaderParameter(r,t.COMPILE_STATUS))}queryWebGL(t){const e=t.getContext("webgl");e&&(this.maxTextureSize=e.getParameter(e.MAX_TEXTURE_SIZE),this.GLPrecision=["highp","mediump","lowp"].find(s=>this.testPrecision(e,s)),e.getExtension("WEBGL_lose_context").loseContext(),Gt("log","WebGL: max texture size ".concat(this.maxTextureSize)))}isSupported(t){return!!this.maxTextureSize&&this.maxTextureSize>=t}}const Pn={};let Wr;const Ot=()=>Wr||(Wr={document,window,isTouchSupported:"ontouchstart"in window||"ontouchstart"in document||window&&window.navigator&&window.navigator.maxTouchPoints>0,WebGLProbe:new jn,dispose(){},copyPasteData:Pn}),me=()=>Ot().document,js=()=>Ot().window,bi=()=>{var a;return Math.max((a=E.devicePixelRatio)!==null&&a!==void 0?a:js().devicePixelRatio,1)},ze=new class{constructor(){p(this,"charWidthsCache",{}),p(this,"boundsOfCurveCache",{})}getFontCache(a){let{fontFamily:t,fontStyle:e,fontWeight:s}=a;t=t.toLowerCase(),this.charWidthsCache[t]||(this.charWidthsCache[t]={});const r=this.charWidthsCache[t],i="".concat(e.toLowerCase(),"_").concat((s+"").toLowerCase());return r[i]||(r[i]={}),r[i]}clearFontCache(a){(a=(a||"").toLowerCase())?this.charWidthsCache[a]&&delete this.charWidthsCache[a]:this.charWidthsCache={}}limitDimsByArea(a){const{perfLimitSizeTotal:t}=E,e=Math.sqrt(t*a);return[Math.floor(e),Math.floor(t/e)]}},nr="6.6.6";function fs(){}const Qe=Math.PI/2,xs=2*Math.PI,_r=Math.PI/180,tt=Object.freeze([1,0,0,1,0,0]),xr=16,zt=.4477152502,k="center",j="left",rt="top",or="bottom",z="right",it="none",Cr=/\r?\n/,Si="moving",Ps="scaling",wi="rotating",br="rotate",Ti="skewing",Ue="resizing",An="modifyPoly",Fn="modifyPath",Cs="changed",As="scale",ot="scaleX",gt="scaleY",ve="skewX",ye="skewY",K="fill",nt="stroke",bs="modified",oe="json",Hs="svg",T=new class{constructor(){this[oe]=new Map,this[Hs]=new Map}has(a){return this[oe].has(a)}getClass(a){const t=this[oe].get(a);if(!t)throw new Tt("No class registered for ".concat(a));return t}setClass(a,t){t?this[oe].set(t,a):(this[oe].set(a.type,a),this[oe].set(a.type.toLowerCase(),a))}getSVGClass(a){return this[Hs].get(a)}setSVGClass(a,t){this[Hs].set(t??a.type.toLowerCase(),a)}},Ss=new class extends Array{remove(a){const t=this.indexOf(a);t>-1&&this.splice(t,1)}cancelAll(){const a=this.splice(0);return a.forEach(t=>t.abort()),a}cancelByCanvas(a){if(!a)return[];const t=this.filter(e=>{var s;return e.target===a||typeof e.target=="object"&&((s=e.target)===null||s===void 0?void 0:s.canvas)===a});return t.forEach(e=>e.abort()),t}cancelByTarget(a){if(!a)return[];const t=this.filter(e=>e.target===a);return t.forEach(e=>e.abort()),t}};class Ln{constructor(){p(this,"__eventListeners",{})}on(t,e){if(this.__eventListeners||(this.__eventListeners={}),typeof t=="object")return Object.entries(t).forEach(s=>{let[r,i]=s;this.on(r,i)}),()=>this.off(t);if(e){const s=t;return this.__eventListeners[s]||(this.__eventListeners[s]=[]),this.__eventListeners[s].push(e),()=>this.off(s,e)}return()=>!1}once(t,e){if(typeof t=="object"){const s=[];return Object.entries(t).forEach(r=>{let[i,n]=r;s.push(this.once(i,n))}),()=>s.forEach(r=>r())}if(e){const s=this.on(t,function(){for(var r=arguments.length,i=new Array(r),n=0;n<r;n++)i[n]=arguments[n];e.call(this,...i),s()});return s}return()=>!1}_removeEventListener(t,e){if(this.__eventListeners[t])if(e){const s=this.__eventListeners[t],r=s.indexOf(e);r>-1&&s.splice(r,1)}else this.__eventListeners[t]=[]}off(t,e){if(this.__eventListeners)if(t===void 0)for(const s in this.__eventListeners)this._removeEventListener(s);else typeof t=="object"?Object.entries(t).forEach(s=>{let[r,i]=s;this._removeEventListener(r,i)}):this._removeEventListener(t,e)}fire(t,e){var s;if(!this.__eventListeners)return;const r=(s=this.__eventListeners[t])===null||s===void 0?void 0:s.concat();if(r)for(let i=0;i<r.length;i++)r[i].call(this,e||{})}}const he=(a,t)=>{const e=a.indexOf(t);return e!==-1&&a.splice(e,1),a},Lt=a=>{if(a===0)return 1;switch(Math.abs(a)/Qe){case 1:case 3:return 0;case 2:return-1}return Math.cos(a)},Rt=a=>{if(a===0)return 0;const t=a/Qe,e=Math.sign(a);switch(t){case 1:return e;case 2:return 0;case 3:return-e}return Math.sin(a)};class _{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;typeof t=="object"?(this.x=t.x,this.y=t.y):(this.x=t,this.y=e)}add(t){return new _(this.x+t.x,this.y+t.y)}addEquals(t){return this.x+=t.x,this.y+=t.y,this}scalarAdd(t){return new _(this.x+t,this.y+t)}scalarAddEquals(t){return this.x+=t,this.y+=t,this}subtract(t){return new _(this.x-t.x,this.y-t.y)}subtractEquals(t){return this.x-=t.x,this.y-=t.y,this}scalarSubtract(t){return new _(this.x-t,this.y-t)}scalarSubtractEquals(t){return this.x-=t,this.y-=t,this}multiply(t){return new _(this.x*t.x,this.y*t.y)}scalarMultiply(t){return new _(this.x*t,this.y*t)}scalarMultiplyEquals(t){return this.x*=t,this.y*=t,this}divide(t){return new _(this.x/t.x,this.y/t.y)}scalarDivide(t){return new _(this.x/t,this.y/t)}scalarDivideEquals(t){return this.x/=t,this.y/=t,this}eq(t){return this.x===t.x&&this.y===t.y}lt(t){return this.x<t.x&&this.y<t.y}lte(t){return this.x<=t.x&&this.y<=t.y}gt(t){return this.x>t.x&&this.y>t.y}gte(t){return this.x>=t.x&&this.y>=t.y}lerp(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:.5;return e=Math.max(Math.min(1,e),0),new _(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)}distanceFrom(t){const e=this.x-t.x,s=this.y-t.y;return Math.sqrt(e*e+s*s)}midPointFrom(t){return this.lerp(t)}min(t){return new _(Math.min(this.x,t.x),Math.min(this.y,t.y))}max(t){return new _(Math.max(this.x,t.x),Math.max(this.y,t.y))}toString(){return"".concat(this.x,",").concat(this.y)}setXY(t,e){return this.x=t,this.y=e,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setFromPoint(t){return this.x=t.x,this.y=t.y,this}swap(t){const e=this.x,s=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=s}clone(){return new _(this.x,this.y)}rotate(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Sr;const s=Rt(t),r=Lt(t),i=this.subtract(e);return new _(i.x*r-i.y*s,i.x*s+i.y*r).add(e)}transform(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return new _(t[0]*this.x+t[2]*this.y+(e?0:t[4]),t[1]*this.x+t[3]*this.y+(e?0:t[5]))}}const Sr=new _(0,0),ps=a=>!!a&&Array.isArray(a._objects);function Oi(a){class t extends a{constructor(){super(...arguments),p(this,"_objects",[])}_onObjectAdded(s){}_onObjectRemoved(s){}_onStackOrderChanged(s){}add(){for(var s=arguments.length,r=new Array(s),i=0;i<s;i++)r[i]=arguments[i];const n=this._objects.push(...r);return r.forEach(o=>this._onObjectAdded(o)),n}insertAt(s){for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return this._objects.splice(s,0,...i),i.forEach(o=>this._onObjectAdded(o)),this._objects.length}remove(){const s=this._objects,r=[];for(var i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return n.forEach(h=>{const l=s.indexOf(h);l!==-1&&(s.splice(l,1),r.push(h),this._onObjectRemoved(h))}),r}forEachObject(s){this.getObjects().forEach((r,i,n)=>s(r,i,n))}getObjects(){for(var s=arguments.length,r=new Array(s),i=0;i<s;i++)r[i]=arguments[i];return r.length===0?[...this._objects]:this._objects.filter(n=>n.isType(...r))}item(s){return this._objects[s]}isEmpty(){return this._objects.length===0}size(){return this._objects.length}contains(s,r){return!!this._objects.includes(s)||!!r&&this._objects.some(i=>i instanceof t&&i.contains(s,!0))}complexity(){return this._objects.reduce((s,r)=>s+=r.complexity?r.complexity():0,0)}sendObjectToBack(s){return!(!s||s===this._objects[0])&&(he(this._objects,s),this._objects.unshift(s),this._onStackOrderChanged(s),!0)}bringObjectToFront(s){return!(!s||s===this._objects[this._objects.length-1])&&(he(this._objects,s),this._objects.push(s),this._onStackOrderChanged(s),!0)}sendObjectBackwards(s,r){if(!s)return!1;const i=this._objects.indexOf(s);if(i!==0){const n=this.findNewLowerIndex(s,i,r);return he(this._objects,s),this._objects.splice(n,0,s),this._onStackOrderChanged(s),!0}return!1}bringObjectForward(s,r){if(!s)return!1;const i=this._objects.indexOf(s);if(i!==this._objects.length-1){const n=this.findNewUpperIndex(s,i,r);return he(this._objects,s),this._objects.splice(n,0,s),this._onStackOrderChanged(s),!0}return!1}moveObjectTo(s,r){return s!==this._objects[r]&&(he(this._objects,s),this._objects.splice(r,0,s),this._onStackOrderChanged(s),!0)}findNewLowerIndex(s,r,i){let n;if(i){n=r;for(let o=r-1;o>=0;--o)if(s.isOverlapping(this._objects[o])){n=o;break}}else n=r-1;return n}findNewUpperIndex(s,r,i){let n;if(i){n=r;for(let o=r+1;o<this._objects.length;++o)if(s.isOverlapping(this._objects[o])){n=o;break}}else n=r+1;return n}collectObjects(s){let{left:r,top:i,width:n,height:o}=s,{includeIntersecting:h=!0}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const l=[],c=new _(r,i),u=c.add(new _(n,o));for(let g=this._objects.length-1;g>=0;g--){const d=this._objects[g];d.selectable&&d.visible&&(h&&d.intersectsWithRect(c,u)||d.isContainedWithinRect(c,u)||h&&d.containsPoint(c)||h&&d.containsPoint(u))&&l.push(d)}return l}}return t}class Di extends Ln{_setOptions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};for(const e in t)this.set(e,t[e])}_setObject(t){for(const e in t)this._set(e,t[e])}set(t,e){return typeof t=="object"?this._setObject(t):this._set(t,e),this}_set(t,e){this[t]=e}toggle(t){const e=this.get(t);return typeof e=="boolean"&&this.set(t,!e),this}get(t){return this[t]}}function ms(a){return js().requestAnimationFrame(a)}function Rn(a){return js().cancelAnimationFrame(a)}let Bn=0;const Nt=()=>Bn++,Bt=()=>{const a=me().createElement("canvas");if(!a||a.getContext===void 0)throw new Tt("Failed to create `canvas` element");return a},In=()=>me().createElement("img"),dt=a=>{const t=Bt();return t.width=a.width,t.height=a.height,t},ki=(a,t,e)=>a.toDataURL("image/".concat(t),e),Mi=(a,t,e)=>new Promise((s,r)=>{a.toBlob(s,"image/".concat(t),e)}),H=a=>a*_r,It=a=>a/_r,Xn=a=>a.every((t,e)=>t===tt[e]),st=(a,t,e)=>new _(a).transform(t,e),xt=a=>{const t=1/(a[0]*a[3]-a[1]*a[2]),e=[t*a[3],-t*a[1],-t*a[2],t*a[0],0,0],{x:s,y:r}=new _(a[4],a[5]).transform(e,!0);return e[4]=-s,e[5]=-r,e},J=(a,t,e)=>[a[0]*t[0]+a[2]*t[1],a[1]*t[0]+a[3]*t[1],a[0]*t[2]+a[2]*t[3],a[1]*t[2]+a[3]*t[3],e?0:a[0]*t[4]+a[2]*t[5]+a[4],e?0:a[1]*t[4]+a[3]*t[5]+a[5]],wr=(a,t)=>a.reduceRight((e,s)=>s&&e?J(s,e,t):s||e,void 0)||tt.concat(),Ei=a=>{let[t,e]=a;return Math.atan2(e,t)},ws=a=>{const t=Ei(a),e=Math.pow(a[0],2)+Math.pow(a[1],2),s=Math.sqrt(e),r=(a[0]*a[3]-a[2]*a[1])/s,i=Math.atan2(a[0]*a[2]+a[1]*a[3],e);return{angle:It(t),scaleX:s,scaleY:r,skewX:It(i),skewY:0,translateX:a[4]||0,translateY:a[5]||0}},ts=function(a){return[1,0,0,1,a,arguments.length>1&&arguments[1]!==void 0?arguments[1]:0]};function _e(){let{angle:a=0}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{x:t=0,y:e=0}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const s=H(a),r=Lt(s),i=Rt(s);return[r,i,-i,r,t?t-(r*t-i*e):0,e?e-(i*t+r*e):0]}const Tr=function(a){return[a,0,0,arguments.length>1&&arguments[1]!==void 0?arguments[1]:a,0,0]},ji=a=>Math.tan(H(a)),Pi=a=>[1,0,ji(a),1,0,0],Ai=a=>[1,ji(a),0,1,0,0],Fs=a=>{let{scaleX:t=1,scaleY:e=1,flipX:s=!1,flipY:r=!1,skewX:i=0,skewY:n=0}=a,o=Tr(s?-t:t,r?-e:e);return i&&(o=J(o,Pi(i),!0)),n&&(o=J(o,Ai(n),!0)),o},Wn=a=>{const{translateX:t=0,translateY:e=0,angle:s=0}=a;let r=ts(t,e);s&&(r=J(r,_e({angle:s})));const i=Fs(a);return Xn(i)||(r=J(r,i)),r},vs=function(a){let{signal:t,crossOrigin:e=null}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise(function(s,r){if(t&&t.aborted)return r(new Mn("loadImage"));const i=In();let n;t&&(n=function(h){i.src="",r(h)},t.addEventListener("abort",n,{once:!0}));const o=function(){i.onload=i.onerror=null,n&&(t==null||t.removeEventListener("abort",n)),s(i)};a?(i.onload=o,i.onerror=function(){n&&(t==null||t.removeEventListener("abort",n)),r(new Tt("Error loading ".concat(i.src)))},e&&(i.crossOrigin=e),i.src=a):o()})},qe=function(a){let{signal:t,reviver:e=fs}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise((s,r)=>{const i=[];t&&t.addEventListener("abort",r,{once:!0}),Promise.all(a.map(n=>T.getClass(n.type).fromObject(n,{signal:t}).then(o=>(e(n,o),i.push(o),o)))).then(s).catch(n=>{i.forEach(o=>{o.dispose&&o.dispose()}),r(n)}).finally(()=>{t&&t.removeEventListener("abort",r)})})},Ls=function(a){let{signal:t}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise((e,s)=>{const r=[];t&&t.addEventListener("abort",s,{once:!0});const i=Object.values(a).map(o=>o&&o.type&&T.has(o.type)?qe([o],{signal:t}).then(h=>{let[l]=h;return r.push(l),l}):o),n=Object.keys(a);Promise.all(i).then(o=>o.reduce((h,l,c)=>(h[n[c]]=l,h),{})).then(e).catch(o=>{r.forEach(h=>{h.dispose&&h.dispose()}),s(o)}).finally(()=>{t&&t.removeEventListener("abort",s)})})},xe=function(a){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:[]).reduce((t,e)=>(e in a&&(t[e]=a[e]),t),{})},Or=(a,t)=>Object.keys(a).reduce((e,s)=>(t(a[s],s,a)&&(e[s]=a[s]),e),{}),B=(a,t)=>parseFloat(Number(a).toFixed(t)),Ke=a=>"matrix("+a.map(t=>B(t,E.NUM_FRACTION_DIGITS)).join(" ")+")",ut=a=>!!a&&a.toLive!==void 0,Yr=a=>!!a&&typeof a.toObject=="function",Vr=a=>!!a&&a.offsetX!==void 0&&"source"in a,Jt=a=>!!a&&"multiSelectionStacking"in a;function Fi(a){const t=a&&_t(a);let e=0,s=0;if(!a||!t)return{left:e,top:s};let r=a;const i=t.documentElement,n=t.body||{scrollLeft:0,scrollTop:0};for(;r&&(r.parentNode||r.host)&&(r=r.parentNode||r.host,r===t?(e=n.scrollLeft||i.scrollLeft||0,s=n.scrollTop||i.scrollTop||0):(e+=r.scrollLeft||0,s+=r.scrollTop||0),r.nodeType!==1||r.style.position!=="fixed"););return{left:e,top:s}}const _t=a=>a.ownerDocument||null,Li=a=>{var t;return((t=a.ownerDocument)===null||t===void 0?void 0:t.defaultView)||null},Ri=function(a,t,e){let{width:s,height:r}=e,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;a.width=s,a.height=r,i>1&&(a.setAttribute("width",(s*i).toString()),a.setAttribute("height",(r*i).toString()),t.scale(i,i))},ar=(a,t)=>{let{width:e,height:s}=t;e&&(a.style.width=typeof e=="number"?"".concat(e,"px"):e),s&&(a.style.height=typeof s=="number"?"".concat(s,"px"):s)};function zr(a){return a.onselectstart!==void 0&&(a.onselectstart=()=>!1),a.style.userSelect=it,a}class Bi{constructor(t){p(this,"_originalCanvasStyle",void 0),p(this,"lower",void 0);const e=this.createLowerCanvas(t);this.lower={el:e,ctx:e.getContext("2d")}}createLowerCanvas(t){const e=(s=t)&&s.getContext!==void 0?t:t&&me().getElementById(t)||Bt();var s;if(e.hasAttribute("data-fabric"))throw new Tt("Trying to initialize a canvas that has already been initialized. Did you forget to dispose the canvas?");return this._originalCanvasStyle=e.style.cssText,e.setAttribute("data-fabric","main"),e.classList.add("lower-canvas"),e}cleanupDOM(t){let{width:e,height:s}=t;const{el:r}=this.lower;r.classList.remove("lower-canvas"),r.removeAttribute("data-fabric"),r.setAttribute("width","".concat(e)),r.setAttribute("height","".concat(s)),r.style.cssText=this._originalCanvasStyle||"",this._originalCanvasStyle=void 0}setDimensions(t,e){const{el:s,ctx:r}=this.lower;Ri(s,r,t,e)}setCSSDimensions(t){ar(this.lower.el,t)}calcOffset(){return function(t){var e;const s=t&&_t(t),r={left:0,top:0};if(!s)return r;const i=((e=Li(t))===null||e===void 0?void 0:e.getComputedStyle(t,null))||{};r.left+=parseInt(i.borderLeftWidth,10)||0,r.top+=parseInt(i.borderTopWidth,10)||0,r.left+=parseInt(i.paddingLeft,10)||0,r.top+=parseInt(i.paddingTop,10)||0;let n={left:0,top:0};const o=s.documentElement;t.getBoundingClientRect!==void 0&&(n=t.getBoundingClientRect());const h=Fi(t);return{left:n.left+h.left-(o.clientLeft||0)+r.left,top:n.top+h.top-(o.clientTop||0)+r.top}}(this.lower.el)}dispose(){Ot().dispose(this.lower.el),delete this.lower}}const Yn={backgroundVpt:!0,backgroundColor:"",overlayVpt:!0,overlayColor:"",includeDefaultValues:!0,svgViewportTransformation:!0,renderOnAddRemove:!0,skipOffscreen:!0,enableRetinaScaling:!0,imageSmoothingEnabled:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,viewportTransform:[...tt]};class es extends Oi(Di){get lowerCanvasEl(){var t;return(t=this.elements.lower)===null||t===void 0?void 0:t.el}get contextContainer(){var t;return(t=this.elements.lower)===null||t===void 0?void 0:t.ctx}static getDefaults(){return es.ownDefaults}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,this.constructor.getDefaults()),this.set(e),this.initElements(t),this._setDimensionsImpl({width:this.width||this.elements.lower.el.width||0,height:this.height||this.elements.lower.el.height||0}),this.skipControlsDrawing=!1,this.viewportTransform=[...this.viewportTransform],this.calcViewportBoundaries()}initElements(t){this.elements=new Bi(t)}add(){const t=super.add(...arguments);return arguments.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}insertAt(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),r=1;r<e;r++)s[r-1]=arguments[r];const i=super.insertAt(t,...s);return s.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),i}remove(){const t=super.remove(...arguments);return t.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}_onObjectAdded(t){t.canvas&&t.canvas!==this&&(Gt("warn",`Canvas is trying to add an object that belongs to a different canvas.
Resulting to default behavior: removing object from previous canvas and adding to new canvas`),t.canvas.remove(t)),t._set("canvas",this),t.setCoords(),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t){t._set("canvas",void 0),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onStackOrderChanged(){this.renderOnAddRemove&&this.requestRenderAll()}getRetinaScaling(){return this.enableRetinaScaling?bi():1}calcOffset(){return this._offset=this.elements.calcOffset()}getWidth(){return this.width}getHeight(){return this.height}setWidth(t,e){return this.setDimensions({width:t},e)}setHeight(t,e){return this.setDimensions({height:t},e)}_setDimensionsImpl(t){let{cssOnly:e=!1,backstoreOnly:s=!1}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!e){const r=m({width:this.width,height:this.height},t);this.elements.setDimensions(r,this.getRetinaScaling()),this.hasLostContext=!0,this.width=r.width,this.height=r.height}s||this.elements.setCSSDimensions(t),this.calcOffset()}setDimensions(t,e){this._setDimensionsImpl(t,e),e&&e.cssOnly||this.requestRenderAll()}getZoom(){return this.viewportTransform[0]}setViewportTransform(t){this.viewportTransform=t,this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll()}zoomToPoint(t,e){const s=t,r=[...this.viewportTransform],i=st(t,xt(r));r[0]=e,r[3]=e;const n=st(i,r);r[4]+=s.x-n.x,r[5]+=s.y-n.y,this.setViewportTransform(r)}setZoom(t){this.zoomToPoint(new _(0,0),t)}absolutePan(t){const e=[...this.viewportTransform];return e[4]=-t.x,e[5]=-t.y,this.setViewportTransform(e)}relativePan(t){return this.absolutePan(new _(-t.x-this.viewportTransform[4],-t.y-this.viewportTransform[5]))}getElement(){return this.elements.lower.el}clearContext(t){t.clearRect(0,0,this.width,this.height)}getContext(){return this.elements.lower.ctx}clear(){this.remove(...this.getObjects()),this.backgroundImage=void 0,this.overlayImage=void 0,this.backgroundColor="",this.overlayColor="",this.clearContext(this.getContext()),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll()}renderAll(){this.cancelRequestedRender(),this.destroyed||this.renderCanvas(this.getContext(),this._objects)}renderAndReset(){this.nextRenderHandle=0,this.renderAll()}requestRenderAll(){this.nextRenderHandle||this.disposed||this.destroyed||(this.nextRenderHandle=ms(()=>this.renderAndReset()))}calcViewportBoundaries(){const t=this.width,e=this.height,s=xt(this.viewportTransform),r=st({x:0,y:0},s),i=st({x:t,y:e},s),n=r.min(i),o=r.max(i);return this.vptCoords={tl:n,tr:new _(o.x,n.y),bl:new _(n.x,o.y),br:o}}cancelRequestedRender(){this.nextRenderHandle&&(Rn(this.nextRenderHandle),this.nextRenderHandle=0)}drawControls(t){}renderCanvas(t,e){if(this.destroyed)return;const s=this.viewportTransform,r=this.clipPath;this.calcViewportBoundaries(),this.clearContext(t),t.imageSmoothingEnabled=this.imageSmoothingEnabled,t.patternQuality="best",this.fire("before:render",{ctx:t}),this._renderBackground(t),t.save(),t.transform(s[0],s[1],s[2],s[3],s[4],s[5]),this._renderObjects(t,e),t.restore(),this.controlsAboveOverlay||this.skipControlsDrawing||this.drawControls(t),r&&(r._set("canvas",this),r.shouldCache(),r._transformDone=!0,r.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(t,r)),this._renderOverlay(t),this.controlsAboveOverlay&&!this.skipControlsDrawing&&this.drawControls(t),this.fire("after:render",{ctx:t}),this.__cleanupTask&&(this.__cleanupTask(),this.__cleanupTask=void 0)}drawClipPathOnCanvas(t,e){const s=this.viewportTransform;t.save(),t.transform(...s),t.globalCompositeOperation="destination-in",e.transform(t),t.scale(1/e.zoomX,1/e.zoomY),t.drawImage(e._cacheCanvas,-e.cacheTranslationX,-e.cacheTranslationY),t.restore()}_renderObjects(t,e){for(let s=0,r=e.length;s<r;++s)e[s]&&e[s].render(t)}_renderBackgroundOrOverlay(t,e){const s=this["".concat(e,"Color")],r=this["".concat(e,"Image")],i=this.viewportTransform,n=this["".concat(e,"Vpt")];if(!s&&!r)return;const o=ut(s);if(s){if(t.save(),t.beginPath(),t.moveTo(0,0),t.lineTo(this.width,0),t.lineTo(this.width,this.height),t.lineTo(0,this.height),t.closePath(),t.fillStyle=o?s.toLive(t):s,n&&t.transform(...i),o){t.transform(1,0,0,1,s.offsetX||0,s.offsetY||0);const h=s.gradientTransform||s.patternTransform;h&&t.transform(...h)}t.fill(),t.restore()}if(r){t.save();const{skipOffscreen:h}=this;this.skipOffscreen=n,n&&t.transform(...i),r.render(t),this.skipOffscreen=h,t.restore()}}_renderBackground(t){this._renderBackgroundOrOverlay(t,"background")}_renderOverlay(t){this._renderBackgroundOrOverlay(t,"overlay")}getCenter(){return{top:this.height/2,left:this.width/2}}getCenterPoint(){return new _(this.width/2,this.height/2)}centerObjectH(t){return this._centerObject(t,new _(this.getCenterPoint().x,t.getCenterPoint().y))}centerObjectV(t){return this._centerObject(t,new _(t.getCenterPoint().x,this.getCenterPoint().y))}centerObject(t){return this._centerObject(t,this.getCenterPoint())}viewportCenterObject(t){return this._centerObject(t,this.getVpCenter())}viewportCenterObjectH(t){return this._centerObject(t,new _(this.getVpCenter().x,t.getCenterPoint().y))}viewportCenterObjectV(t){return this._centerObject(t,new _(t.getCenterPoint().x,this.getVpCenter().y))}getVpCenter(){return st(this.getCenterPoint(),xt(this.viewportTransform))}_centerObject(t,e){t.setXY(e,k,k),t.setCoords(),this.renderOnAddRemove&&this.requestRenderAll()}toDatalessJSON(t){return this.toDatalessObject(t)}toObject(t){return this._toObjectMethod("toObject",t)}toJSON(){return this.toObject()}toDatalessObject(t){return this._toObjectMethod("toDatalessObject",t)}_toObjectMethod(t,e){const s=this.clipPath,r=s&&!s.excludeFromExport?this._toObject(s,t,e):null;return m(m(m({version:nr},xe(this,e)),{},{objects:this._objects.filter(i=>!i.excludeFromExport).map(i=>this._toObject(i,t,e))},this.__serializeBgOverlay(t,e)),r?{clipPath:r}:null)}_toObject(t,e,s){let r;this.includeDefaultValues||(r=t.includeDefaultValues,t.includeDefaultValues=!1);const i=t[e](s);return this.includeDefaultValues||(t.includeDefaultValues=!!r),i}__serializeBgOverlay(t,e){const s={},r=this.backgroundImage,i=this.overlayImage,n=this.backgroundColor,o=this.overlayColor;return ut(n)?n.excludeFromExport||(s.background=n.toObject(e)):n&&(s.background=n),ut(o)?o.excludeFromExport||(s.overlay=o.toObject(e)):o&&(s.overlay=o),r&&!r.excludeFromExport&&(s.backgroundImage=this._toObject(r,t,e)),i&&!i.excludeFromExport&&(s.overlayImage=this._toObject(i,t,e)),s}toSVG(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;t.reviver=e;const s=[];return this._setSVGPreamble(s,t),this._setSVGHeader(s,t),this.clipPath&&s.push('<g clip-path="url(#'.concat(this.clipPath.clipPathId,`)" >
`)),this._setSVGBgOverlayColor(s,"background"),this._setSVGBgOverlayImage(s,"backgroundImage",e),this._setSVGObjects(s,e),this.clipPath&&s.push(`</g>
`),this._setSVGBgOverlayColor(s,"overlay"),this._setSVGBgOverlayImage(s,"overlayImage",e),s.push("</svg>"),s.join("")}_setSVGPreamble(t,e){e.suppressPreamble||t.push('<?xml version="1.0" encoding="',e.encoding||"UTF-8",`" standalone="no" ?>
`,'<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ',`"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
`)}_setSVGHeader(t,e){const s=e.width||"".concat(this.width),r=e.height||"".concat(this.height),i=E.NUM_FRACTION_DIGITS,n=e.viewBox;let o;if(n)o='viewBox="'.concat(n.x," ").concat(n.y," ").concat(n.width," ").concat(n.height,'" ');else if(this.svgViewportTransformation){const h=this.viewportTransform;o='viewBox="'.concat(B(-h[4]/h[0],i)," ").concat(B(-h[5]/h[3],i)," ").concat(B(this.width/h[0],i)," ").concat(B(this.height/h[3],i),'" ')}else o='viewBox="0 0 '.concat(this.width," ").concat(this.height,'" ');t.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',s,'" ','height="',r,'" ',o,`xml:space="preserve">
`,"<desc>Created with Fabric.js ",nr,`</desc>
`,`<defs>
`,this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(e),`</defs>
`)}createSVGClipPathMarkup(t){const e=this.clipPath;return e?(e.clipPathId="CLIPPATH_".concat(Nt()),'<clipPath id="'.concat(e.clipPathId,`" >
`).concat(e.toClipPathSVG(t.reviver),`</clipPath>
`)):""}createSVGRefElementsMarkup(){return["background","overlay"].map(t=>{const e=this["".concat(t,"Color")];if(ut(e)){const s=this["".concat(t,"Vpt")],r=this.viewportTransform,i={isType:()=>!1,width:this.width/(s?r[0]:1),height:this.height/(s?r[3]:1)};return e.toSVG(i,{additionalTransform:s?Ke(r):""})}}).join("")}createSVGFontFacesMarkup(){const t=[],e={},s=E.fontPaths;this._objects.forEach(function i(n){t.push(n),ps(n)&&n._objects.forEach(i)}),t.forEach(i=>{if(!(n=i)||typeof n._renderText!="function")return;var n;const{styles:o,fontFamily:h}=i;!e[h]&&s[h]&&(e[h]=!0,o&&Object.values(o).forEach(l=>{Object.values(l).forEach(c=>{let{fontFamily:u=""}=c;!e[u]&&s[u]&&(e[u]=!0)})}))});const r=Object.keys(e).map(i=>`		@font-face {
			font-family: '`.concat(i,`';
			src: url('`).concat(s[i],`');
		}
`)).join("");return r?`	<style type="text/css"><![CDATA[
`.concat(r,`]]></style>
`):""}_setSVGObjects(t,e){this.forEachObject(s=>{s.excludeFromExport||this._setSVGObject(t,s,e)})}_setSVGObject(t,e,s){t.push(e.toSVG(s))}_setSVGBgOverlayImage(t,e,s){const r=this[e];r&&!r.excludeFromExport&&r.toSVG&&t.push(r.toSVG(s))}_setSVGBgOverlayColor(t,e){const s=this["".concat(e,"Color")];if(s)if(ut(s)){const r=s.repeat||"",i=this.width,n=this.height,o=this["".concat(e,"Vpt")]?Ke(xt(this.viewportTransform)):"";t.push('<rect transform="'.concat(o," translate(").concat(i/2,",").concat(n/2,')" x="').concat(s.offsetX-i/2,'" y="').concat(s.offsetY-n/2,'" width="').concat(r!=="repeat-y"&&r!=="no-repeat"||!Vr(s)?i:s.source.width,'" height="').concat(r!=="repeat-x"&&r!=="no-repeat"||!Vr(s)?n:s.source.height,'" fill="url(#SVGID_').concat(s.id,`)"></rect>
`))}else t.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',s,'"',`></rect>
`)}loadFromJSON(t,e){let{signal:s}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!t)return Promise.reject(new Tt("`json` is undefined"));const r=typeof t=="string"?JSON.parse(t):t,{objects:i=[],backgroundImage:n,background:o,overlayImage:h,overlay:l,clipPath:c}=r,u=this.renderOnAddRemove;return this.renderOnAddRemove=!1,Promise.all([qe(i,{reviver:e,signal:s}),Ls({backgroundImage:n,backgroundColor:o,overlayImage:h,overlayColor:l,clipPath:c},{signal:s})]).then(g=>{let[d,f]=g;return this.clear(),this.add(...d),this.set(r),this.set(f),this.renderOnAddRemove=u,this})}clone(t){const e=this.toObject(t);return this.cloneWithoutData().loadFromJSON(e)}cloneWithoutData(){const t=dt(this);return new this.constructor(t)}toDataURL(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{format:e="png",quality:s=1,multiplier:r=1,enableRetinaScaling:i=!1}=t,n=r*(i?this.getRetinaScaling():1);return ki(this.toCanvasElement(n,t),e,s)}toBlob(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{format:e="png",quality:s=1,multiplier:r=1,enableRetinaScaling:i=!1}=t,n=r*(i?this.getRetinaScaling():1);return Mi(this.toCanvasElement(n,t),e,s)}toCanvasElement(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1,{width:e,height:s,left:r,top:i,filter:n}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const o=(e||this.width)*t,h=(s||this.height)*t,l=this.getZoom(),c=this.width,u=this.height,g=this.skipControlsDrawing,d=l*t,f=this.viewportTransform,v=[d,0,0,d,(f[4]-(r||0))*t,(f[5]-(i||0))*t],y=this.enableRetinaScaling,x=dt({width:o,height:h}),C=n?this._objects.filter(w=>n(w)):this._objects;return this.enableRetinaScaling=!1,this.viewportTransform=v,this.width=o,this.height=h,this.skipControlsDrawing=!0,this.calcViewportBoundaries(),this.renderCanvas(x.getContext("2d"),C),this.viewportTransform=f,this.width=c,this.height=u,this.calcViewportBoundaries(),this.enableRetinaScaling=y,this.skipControlsDrawing=g,x}dispose(){return!this.disposed&&this.elements.cleanupDOM({width:this.width,height:this.height}),Ss.cancelByCanvas(this),this.disposed=!0,new Promise((t,e)=>{const s=()=>{this.destroy(),t(!0)};s.kill=e,this.__cleanupTask&&this.__cleanupTask.kill("aborted"),this.destroyed?t(!1):this.nextRenderHandle?this.__cleanupTask=s:s()})}destroy(){this.destroyed=!0,this.cancelRequestedRender(),this.forEachObject(t=>t.dispose()),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose(),this.backgroundImage=void 0,this.overlayImage&&this.overlayImage.dispose(),this.overlayImage=void 0,this.elements.dispose()}toString(){return"#<Canvas (".concat(this.complexity(),"): { objects: ").concat(this._objects.length," }>")}}p(es,"ownDefaults",Yn);const Vn=["touchstart","touchmove","touchend"],zn=a=>{const t=Fi(a.target),e=function(s){const r=s.changedTouches;return r&&r[0]?r[0]:s}(a);return new _(e.clientX+t.left,e.clientY+t.top)},hr=a=>Vn.includes(a.type)||a.pointerType==="touch",Hr=a=>{a.preventDefault(),a.stopPropagation()},At=a=>{let t=0,e=0,s=0,r=0;for(let i=0,n=a.length;i<n;i++){const{x:o,y:h}=a[i];(o>s||!i)&&(s=o),(o<t||!i)&&(t=o),(h>r||!i)&&(r=h),(h<e||!i)&&(e=h)}return{left:t,top:e,width:s-t,height:r-e}},Hn=["translateX","translateY","scaleX","scaleY"],Gn=(a,t)=>Ts(a,J(t,a.calcOwnMatrix())),Ts=(a,t)=>{const e=ws(t),{translateX:s,translateY:r,scaleX:i,scaleY:n}=e,o=I(e,Hn),h=new _(s,r);a.flipX=!1,a.flipY=!1,Object.assign(a,o),a.set({scaleX:i,scaleY:n}),a.setPositionByOrigin(h,k,k)},Nn=a=>{a.scaleX=1,a.scaleY=1,a.skewX=0,a.skewY=0,a.flipX=!1,a.flipY=!1,a.rotate(0)},Ii=a=>({scaleX:a.scaleX,scaleY:a.scaleY,skewX:a.skewX,skewY:a.skewY,angle:a.angle,left:a.left,flipX:a.flipX,flipY:a.flipY,top:a.top}),Dr=(a,t,e)=>{const s=a/2,r=t/2,i=[new _(-s,-r),new _(s,-r),new _(-s,r),new _(s,r)].map(o=>o.transform(e)),n=At(i);return new _(n.width,n.height)},Rs=function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:tt;return J(xt(arguments.length>1&&arguments[1]!==void 0?arguments[1]:tt),a)},ue=function(a){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:tt,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:tt;return a.transform(Rs(t,e))},Un=function(a){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:tt,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:tt;return a.transform(Rs(t,e),!0)},qn=(a,t,e)=>{const s=Rs(t,e);return Ts(a,J(s,a.calcOwnMatrix())),s},Xi=(a,t)=>{var e;const{transform:{target:s}}=t;(e=s.canvas)===null||e===void 0||e.fire("object:".concat(a),m(m({},t),{},{target:s})),s.fire(a,t)},Kn={left:-.5,top:-.5,center:0,bottom:.5,right:.5},N=a=>typeof a=="string"?Kn[a]:a-.5,Os="not-allowed";function Wi(a){return N(a.originX)===N(k)&&N(a.originY)===N(k)}function Gr(a){return .5-N(a)}const Ct=(a,t)=>a[t],Yi=(a,t,e,s)=>({e:a,transform:t,pointer:new _(e,s)});function Vi(a,t){const e=a.getTotalAngle()+It(Math.atan2(t.y,t.x))+360;return Math.round(e%360/45)}function kr(a,t,e,s,r){var i;let{target:n,corner:o}=a;const h=n.controls[o],l=((i=n.canvas)===null||i===void 0?void 0:i.getZoom())||1,c=n.padding/l,u=function(g,d,f,v){const y=g.getRelativeCenterPoint(),x=f!==void 0&&v!==void 0?g.translateToGivenOrigin(y,k,k,f,v):new _(g.left,g.top);return(g.angle?d.rotate(-H(g.angle),y):d).subtract(x)}(n,new _(s,r),t,e);return u.x>=c&&(u.x-=c),u.x<=-c&&(u.x+=c),u.y>=c&&(u.y-=c),u.y<=c&&(u.y+=c),u.x-=h.offsetX,u.y-=h.offsetY,u}const Jn=(a,t,e,s)=>{const{target:r,offsetX:i,offsetY:n}=t,o=e-i,h=s-n,l=!Ct(r,"lockMovementX")&&r.left!==o,c=!Ct(r,"lockMovementY")&&r.top!==h;return l&&r.set(j,o),c&&r.set(rt,h),(l||c)&&Xi(Si,Yi(a,t,e,s)),l||c},Nr={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#0FF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000",blanchedalmond:"#FFEBCD",blue:"#00F",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#0FF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#F0F",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#789",lightslategrey:"#789",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#0F0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#F0F",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#639",red:"#F00",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFF",whitesmoke:"#F5F5F5",yellow:"#FF0",yellowgreen:"#9ACD32"},Gs=(a,t,e)=>(e<0&&(e+=1),e>1&&(e-=1),e<1/6?a+6*(t-a)*e:e<.5?t:e<2/3?a+(t-a)*(2/3-e)*6:a),Ur=(a,t,e,s)=>{a/=255,t/=255,e/=255;const r=Math.max(a,t,e),i=Math.min(a,t,e);let n,o;const h=(r+i)/2;if(r===i)n=o=0;else{const l=r-i;switch(o=h>.5?l/(2-r-i):l/(r+i),r){case a:n=(t-e)/l+(t<e?6:0);break;case t:n=(e-a)/l+2;break;case e:n=(a-t)/l+4}n/=6}return[Math.round(360*n),Math.round(100*o),Math.round(100*h),s]},qr=function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"1";return parseFloat(a)/(a.endsWith("%")?100:1)},is=a=>Math.min(Math.round(a),255).toString(16).toUpperCase().padStart(2,"0"),Kr=a=>{let[t,e,s,r=1]=a;const i=Math.round(.3*t+.59*e+.11*s);return[i,i,i,r]};class A{constructor(t){if(p(this,"isUnrecognised",!1),t)if(t instanceof A)this.setSource([...t._source]);else if(Array.isArray(t)){const[e,s,r,i=1]=t;this.setSource([e,s,r,i])}else this.setSource(this._tryParsingColor(t));else this.setSource([0,0,0,1])}_tryParsingColor(t){return(t=t.toLowerCase())in Nr&&(t=Nr[t]),t==="transparent"?[255,255,255,0]:A.sourceFromHex(t)||A.sourceFromRgb(t)||A.sourceFromHsl(t)||(this.isUnrecognised=!0)&&[0,0,0,1]}getSource(){return this._source}setSource(t){this._source=t}toRgb(){const[t,e,s]=this.getSource();return"rgb(".concat(t,",").concat(e,",").concat(s,")")}toRgba(){return"rgba(".concat(this.getSource().join(","),")")}toHsl(){const[t,e,s]=Ur(...this.getSource());return"hsl(".concat(t,",").concat(e,"%,").concat(s,"%)")}toHsla(){const[t,e,s,r]=Ur(...this.getSource());return"hsla(".concat(t,",").concat(e,"%,").concat(s,"%,").concat(r,")")}toHex(){return this.toHexa().slice(0,6)}toHexa(){const[t,e,s,r]=this.getSource();return"".concat(is(t)).concat(is(e)).concat(is(s)).concat(is(Math.round(255*r)))}getAlpha(){return this.getSource()[3]}setAlpha(t){return this._source[3]=t,this}toGrayscale(){return this.setSource(Kr(this.getSource())),this}toBlackWhite(t){const[e,,,s]=Kr(this.getSource()),r=e<(t||127)?0:255;return this.setSource([r,r,r,s]),this}overlayWith(t){t instanceof A||(t=new A(t));const e=this.getSource(),s=t.getSource(),[r,i,n]=e.map((o,h)=>Math.round(.5*o+.5*s[h]));return this.setSource([r,i,n,e[3]]),this}static fromRgb(t){return A.fromRgba(t)}static fromRgba(t){return new A(A.sourceFromRgb(t))}static sourceFromRgb(t){const e=t.match(/^rgba?\(\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d{0,3}(?:\.\d+)?%?)\s*)?\)$/i);if(e){const[s,r,i]=e.slice(1,4).map(n=>{const o=parseFloat(n);return n.endsWith("%")?Math.round(2.55*o):o});return[s,r,i,qr(e[4])]}}static fromHsl(t){return A.fromHsla(t)}static fromHsla(t){return new A(A.sourceFromHsl(t))}static sourceFromHsl(t){const e=t.match(/^hsla?\(\s*([+-]?\d{0,3}(?:\.\d+)?(?:deg|turn|rad)?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d*(?:\.\d+)?%?)\s*)?\)$/i);if(!e)return;const s=(A.parseAngletoDegrees(e[1])%360+360)%360/360,r=parseFloat(e[2])/100,i=parseFloat(e[3])/100;let n,o,h;if(r===0)n=o=h=i;else{const l=i<=.5?i*(r+1):i+r-i*r,c=2*i-l;n=Gs(c,l,s+1/3),o=Gs(c,l,s),h=Gs(c,l,s-1/3)}return[Math.round(255*n),Math.round(255*o),Math.round(255*h),qr(e[4])]}static fromHex(t){return new A(A.sourceFromHex(t))}static sourceFromHex(t){if(t.match(/^#?(([0-9a-f]){3,4}|([0-9a-f]{2}){3,4})$/i)){const e=t.slice(t.indexOf("#")+1);let s;s=e.length<=4?e.split("").map(h=>h+h):e.match(/.{2}/g);const[r,i,n,o=255]=s.map(h=>parseInt(h,16));return[r,i,n,o/255]}}static parseAngletoDegrees(t){const e=t.toLowerCase(),s=parseFloat(e);return e.includes("rad")?It(s):e.includes("turn")?360*s:s}}const ge=function(a){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:xr;const e=/\D{0,2}$/.exec(a),s=parseFloat(a),r=E.DPI;switch(e==null?void 0:e[0]){case"mm":return s*r/25.4;case"cm":return s*r/2.54;case"in":return s*r;case"pt":return s*r/72;case"pc":return s*r/72*12;case"em":return s*t;default:return s}},Zn=a=>{const[t,e]=a.trim().split(" "),[s,r]=(i=t)&&i!==it?[i.slice(1,4),i.slice(5,8)]:i===it?[i,i]:["Mid","Mid"];var i;return{meetOrSlice:e||"meet",alignX:s,alignY:r}},Je=function(a,t){let e,s,r=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2];if(t)if(t.toLive)e="url(#SVGID_".concat(t.id,")");else{const i=new A(t),n=i.getAlpha();e=i.toRgb(),n!==1&&(s=n.toString())}else e="none";return r?"".concat(a,": ").concat(e,"; ").concat(s?"".concat(a,"-opacity: ").concat(s,"; "):""):"".concat(a,'="').concat(e,'" ').concat(s?"".concat(a,'-opacity="').concat(s,'" '):"")};class zi{getSvgStyles(t){const e=this.fillRule?this.fillRule:"nonzero",s=this.strokeWidth?this.strokeWidth:"0",r=this.strokeDashArray?this.strokeDashArray.join(" "):it,i=this.strokeDashOffset?this.strokeDashOffset:"0",n=this.strokeLineCap?this.strokeLineCap:"butt",o=this.strokeLineJoin?this.strokeLineJoin:"miter",h=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=this.opacity!==void 0?this.opacity:"1",c=this.visible?"":" visibility: hidden;",u=t?"":this.getSvgFilter(),g=Je(K,this.fill);return[Je(nt,this.stroke),"stroke-width: ",s,"; ","stroke-dasharray: ",r,"; ","stroke-linecap: ",n,"; ","stroke-dashoffset: ",i,"; ","stroke-linejoin: ",o,"; ","stroke-miterlimit: ",h,"; ",g,"fill-rule: ",e,"; ","opacity: ",l,";",u,c].join("")}getSvgFilter(){return this.shadow?"filter: url(#SVGID_".concat(this.shadow.id,");"):""}getSvgCommons(){return[this.id?'id="'.concat(this.id,'" '):"",this.clipPath?'clip-path="url(#'.concat(this.clipPath.clipPathId,')" '):""].join("")}getSvgTransform(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const s=t?this.calcTransformMatrix():this.calcOwnMatrix(),r='transform="'.concat(Ke(s));return"".concat(r).concat(e,'" ')}_toSVG(t){return[""]}toSVG(t){return this._createBaseSVGMarkup(this._toSVG(t),{reviver:t})}toClipPathSVG(t){return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(t),{reviver:t})}_createBaseClipPathSVGMarkup(t){let{reviver:e,additionalTransform:s=""}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=[this.getSvgTransform(!0,s),this.getSvgCommons()].join(""),i=t.indexOf("COMMON_PARTS");return t[i]=r,e?e(t.join("")):t.join("")}_createBaseSVGMarkup(t){let{noStyle:e,reviver:s,withShadow:r,additionalTransform:i}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const n=e?"":'style="'.concat(this.getSvgStyles(),'" '),o=r?'style="'.concat(this.getSvgFilter(),'" '):"",h=this.clipPath,l=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",c=h&&h.absolutePositioned,u=this.stroke,g=this.fill,d=this.shadow,f=[],v=t.indexOf("COMMON_PARTS");let y;h&&(h.clipPathId="CLIPPATH_".concat(Nt()),y='<clipPath id="'.concat(h.clipPathId,`" >
`).concat(h.toClipPathSVG(s),`</clipPath>
`)),c&&f.push("<g ",o,this.getSvgCommons(),` >
`),f.push("<g ",this.getSvgTransform(!1),c?"":o+this.getSvgCommons(),` >
`);const x=[n,l,e?"":this.addPaintOrder()," ",i?'transform="'.concat(i,'" '):""].join("");return t[v]=x,ut(g)&&f.push(g.toSVG(this)),ut(u)&&f.push(u.toSVG(this)),d&&f.push(d.toSVG(this)),h&&f.push(y),f.push(t.join("")),f.push(`</g>
`),c&&f.push(`</g>
`),s?s(f.join("")):f.join("")}addPaintOrder(){return this.paintFirst!==K?' paint-order="'.concat(this.paintFirst,'" '):""}}function Bs(a){return new RegExp("^("+a.join("|")+")\\b","i")}var Jr;const ie=String.raw(Jr||(Jr=Ut(["(?:[-+]?(?:d*.d+|d+.?)(?:[eE][-+]?d+)?)"],["(?:[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?)"]))),$n=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+ie+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+ie+"))?\\s+(.*)"),Qn={cx:j,x:j,r:"radius",cy:rt,y:rt,display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},Ns="font-size",Us="clip-path";Bs(["path","circle","polygon","polyline","ellipse","rect","line","image","text"]);Bs(["symbol","image","marker","pattern","view","svg"]);const Zr=Bs(["symbol","g","a","svg","clipPath","defs"]),to=new _(1,0),Hi=new _,Gi=(a,t)=>a.rotate(t),lr=(a,t)=>new _(t).subtract(a),cr=a=>a.distanceFrom(Hi),ur=(a,t)=>Math.atan2(He(a,t),so(a,t)),eo=a=>ur(to,a),Mr=a=>a.eq(Hi)?a:a.scalarDivide(cr(a)),Ni=function(a){let t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];return Mr(new _(-a.y,a.x).scalarMultiply(t?1:-1))},He=(a,t)=>a.x*t.y-a.y*t.x,so=(a,t)=>a.x*t.x+a.y*t.y,$r=(a,t,e)=>{if(a.eq(t)||a.eq(e))return!0;const s=He(t,e),r=He(t,a),i=He(e,a);return s>=0?r>=0&&i<=0:!(r<=0&&i>=0)},Qr="(-?\\d+(?:\\.\\d*)?(?:px)?(?:\\s?|$))?",ti=new RegExp("(?:\\s|^)"+Qr+Qr+"("+ie+"?(?:px)?)?(?:\\s?|$)(?:$|\\s)");class Ft{constructor(t){const e=typeof t=="string"?Ft.parseShadow(t):t;Object.assign(this,Ft.ownDefaults,e),this.id=Nt()}static parseShadow(t){const e=t.trim(),[,s=0,r=0,i=0]=(ti.exec(e)||[]).map(n=>parseFloat(n)||0);return{color:(e.replace(ti,"")||"rgb(0,0,0)").trim(),offsetX:s,offsetY:r,blur:i}}toString(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")}toSVG(t){const e=Gi(new _(this.offsetX,this.offsetY),H(-t.angle)),s=new A(this.color);let r=40,i=40;return t.width&&t.height&&(r=100*B((Math.abs(e.x)+this.blur)/t.width,E.NUM_FRACTION_DIGITS)+20,i=100*B((Math.abs(e.y)+this.blur)/t.height,E.NUM_FRACTION_DIGITS)+20),t.flipX&&(e.x*=-1),t.flipY&&(e.y*=-1),'<filter id="SVGID_'.concat(this.id,'" y="-').concat(i,'%" height="').concat(100+2*i,'%" x="-').concat(r,'%" width="').concat(100+2*r,`%" >
	<feGaussianBlur in="SourceAlpha" stdDeviation="`).concat(B(this.blur?this.blur/2:0,E.NUM_FRACTION_DIGITS),`"></feGaussianBlur>
	<feOffset dx="`).concat(B(e.x,E.NUM_FRACTION_DIGITS),'" dy="').concat(B(e.y,E.NUM_FRACTION_DIGITS),`" result="oBlur" ></feOffset>
	<feFlood flood-color="`).concat(s.toRgb(),'" flood-opacity="').concat(s.getAlpha(),`"/>
	<feComposite in2="oBlur" operator="in" />
	<feMerge>
		<feMergeNode></feMergeNode>
		<feMergeNode in="SourceGraphic"></feMergeNode>
	</feMerge>
</filter>
`)}toObject(){const t={color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling,type:this.constructor.type},e=Ft.ownDefaults;return this.includeDefaultValues?t:Or(t,(s,r)=>s!==e[r])}static async fromObject(t){return new this(t)}}p(Ft,"ownDefaults",{color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1}),p(Ft,"type","shadow"),T.setClass(Ft,"shadow");const fe=(a,t,e)=>Math.max(a,Math.min(t,e)),ro=[rt,j,ot,gt,"flipX","flipY","originX","originY","angle","opacity","globalCompositeOperation","shadow","visible",ve,ye],Xt=[K,nt,"strokeWidth","strokeDashArray","width","height","paintFirst","strokeUniform","strokeLineCap","strokeDashOffset","strokeLineJoin","strokeMiterLimit","backgroundColor","clipPath"],io={top:0,left:0,width:0,height:0,angle:0,flipX:!1,flipY:!1,scaleX:1,scaleY:1,minScaleLimit:0,skewX:0,skewY:0,originX:j,originY:rt,strokeWidth:1,strokeUniform:!1,padding:0,opacity:1,paintFirst:K,fill:"rgb(0,0,0)",fillRule:"nonzero",stroke:null,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,globalCompositeOperation:"source-over",backgroundColor:"",shadow:null,visible:!0,includeDefaultValues:!0,excludeFromExport:!1,objectCaching:!0,clipPath:void 0,inverted:!1,absolutePositioned:!1,centeredRotation:!0,centeredScaling:!1,dirty:!0},no=(a,t,e,s)=>-e*Math.cos(a/s*Qe)+e+t,oo=()=>!1;class Er{constructor(t){let{startValue:e,byValue:s,duration:r=500,delay:i=0,easing:n=no,onStart:o=fs,onChange:h=fs,onComplete:l=fs,abort:c=oo,target:u}=t;p(this,"_state","pending"),p(this,"durationProgress",0),p(this,"valueProgress",0),this.tick=this.tick.bind(this),this.duration=r,this.delay=i,this.easing=n,this._onStart=o,this._onChange=h,this._onComplete=l,this._abort=c,this.target=u,this.startValue=e,this.byValue=s,this.value=this.startValue,this.endValue=Object.freeze(this.calculate(this.duration).value)}get state(){return this._state}isDone(){return this._state==="aborted"||this._state==="completed"}start(){const t=e=>{this._state==="pending"&&(this.startTime=e||+new Date,this._state="running",this._onStart(),this.tick(this.startTime))};this.register(),this.delay>0?setTimeout(()=>ms(t),this.delay):ms(t)}tick(t){const e=(t||+new Date)-this.startTime,s=Math.min(e,this.duration);this.durationProgress=s/this.duration;const{value:r,valueProgress:i}=this.calculate(s);this.value=Object.freeze(r),this.valueProgress=i,this._state!=="aborted"&&(this._abort(this.value,this.valueProgress,this.durationProgress)?(this._state="aborted",this.unregister()):e>=this.duration?(this.durationProgress=this.valueProgress=1,this._onChange(this.endValue,this.valueProgress,this.durationProgress),this._state="completed",this._onComplete(this.endValue,this.valueProgress,this.durationProgress),this.unregister()):(this._onChange(this.value,this.valueProgress,this.durationProgress),ms(this.tick)))}register(){Ss.push(this)}unregister(){Ss.remove(this)}abort(){this._state="aborted",this.unregister()}}const ao=["startValue","endValue"];class ho extends Er{constructor(t){let{startValue:e=0,endValue:s=100}=t;super(m(m({},I(t,ao)),{},{startValue:e,byValue:s-e}))}calculate(t){const e=this.easing(t,this.startValue,this.byValue,this.duration);return{value:e,valueProgress:Math.abs((e-this.startValue)/this.byValue)}}}const lo=["startValue","endValue"];class co extends Er{constructor(t){let{startValue:e=[0],endValue:s=[100]}=t;super(m(m({},I(t,lo)),{},{startValue:e,byValue:s.map((r,i)=>r-e[i])}))}calculate(t){const e=this.startValue.map((s,r)=>this.easing(t,s,this.byValue[r],this.duration,r));return{value:e,valueProgress:Math.abs((e[0]-this.startValue[0])/this.byValue[0])}}}const uo=["startValue","endValue","easing","onChange","onComplete","abort"],go=(a,t,e,s)=>t+e*(1-Math.cos(a/s*Qe)),qs=a=>a&&((t,e,s)=>a(new A(t).toRgba(),e,s));class fo extends Er{constructor(t){let{startValue:e,endValue:s,easing:r=go,onChange:i,onComplete:n,abort:o}=t,h=I(t,uo);const l=new A(e).getSource(),c=new A(s).getSource();super(m(m({},h),{},{startValue:l,byValue:c.map((u,g)=>u-l[g]),easing:r,onChange:qs(i),onComplete:qs(n),abort:qs(o)}))}calculate(t){const[e,s,r,i]=this.startValue.map((o,h)=>this.easing(t,o,this.byValue[h],this.duration,h)),n=[...[e,s,r].map(Math.round),fe(0,i,1)];return{value:n,valueProgress:n.map((o,h)=>this.byValue[h]!==0?Math.abs((o-this.startValue[h])/this.byValue[h]):0).find(o=>o!==0)||0}}}function Ui(a){const t=(e=>Array.isArray(e.startValue)||Array.isArray(e.endValue))(a)?new co(a):new ho(a);return t.start(),t}function po(a){const t=new fo(a);return t.start(),t}class X{constructor(t){this.status=t,this.points=[]}includes(t){return this.points.some(e=>e.eq(t))}append(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return this.points=this.points.concat(e.filter(r=>!this.includes(r))),this}static isPointContained(t,e,s){let r=arguments.length>3&&arguments[3]!==void 0&&arguments[3];if(e.eq(s))return t.eq(e);if(e.x===s.x)return t.x===e.x&&(r||t.y>=Math.min(e.y,s.y)&&t.y<=Math.max(e.y,s.y));if(e.y===s.y)return t.y===e.y&&(r||t.x>=Math.min(e.x,s.x)&&t.x<=Math.max(e.x,s.x));{const i=lr(e,s),n=lr(e,t).divide(i);return r?Math.abs(n.x)===Math.abs(n.y):n.x===n.y&&n.x>=0&&n.x<=1}}static isPointInPolygon(t,e){const s=new _(t).setX(Math.min(t.x-1,...e.map(i=>i.x)));let r=0;for(let i=0;i<e.length;i++){const n=this.intersectSegmentSegment(e[i],e[(i+1)%e.length],t,s);if(n.includes(t))return!0;r+=+(n.status==="Intersection")}return r%2==1}static intersectLineLine(t,e,s,r){let i=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],n=!(arguments.length>5&&arguments[5]!==void 0)||arguments[5];const o=e.x-t.x,h=e.y-t.y,l=r.x-s.x,c=r.y-s.y,u=t.x-s.x,g=t.y-s.y,d=l*g-c*u,f=o*g-h*u,v=c*o-l*h;if(v!==0){const y=d/v,x=f/v;return(i||0<=y&&y<=1)&&(n||0<=x&&x<=1)?new X("Intersection").append(new _(t.x+y*o,t.y+y*h)):new X}if(d===0||f===0){const y=i||n||X.isPointContained(t,s,r)||X.isPointContained(e,s,r)||X.isPointContained(s,t,e)||X.isPointContained(r,t,e);return new X(y?"Coincident":void 0)}return new X("Parallel")}static intersectSegmentLine(t,e,s,r){return X.intersectLineLine(t,e,s,r,!1,!0)}static intersectSegmentSegment(t,e,s,r){return X.intersectLineLine(t,e,s,r,!1,!1)}static intersectLinePolygon(t,e,s){let r=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3];const i=new X,n=s.length;for(let o,h,l,c=0;c<n;c++){if(o=s[c],h=s[(c+1)%n],l=X.intersectLineLine(t,e,o,h,r,!1),l.status==="Coincident")return l;i.append(...l.points)}return i.points.length>0&&(i.status="Intersection"),i}static intersectSegmentPolygon(t,e,s){return X.intersectLinePolygon(t,e,s,!1)}static intersectPolygonPolygon(t,e){const s=new X,r=t.length,i=[];for(let n=0;n<r;n++){const o=t[n],h=t[(n+1)%r],l=X.intersectSegmentPolygon(o,h,e);l.status==="Coincident"?(i.push(l),s.append(o,h)):s.append(...l.points)}return i.length>0&&i.length===t.length?new X("Coincident"):(s.points.length>0&&(s.status="Intersection"),s)}static intersectPolygonRectangle(t,e,s){const r=e.min(s),i=e.max(s),n=new _(i.x,r.y),o=new _(r.x,i.y);return X.intersectPolygonPolygon(t,[r,n,i,o])}}class mo extends Di{getX(){return this.getXY().x}setX(t){this.setXY(this.getXY().setX(t))}getY(){return this.getXY().y}setY(t){this.setXY(this.getXY().setY(t))}getRelativeX(){return this.left}setRelativeX(t){this.left=t}getRelativeY(){return this.top}setRelativeY(t){this.top=t}getXY(){const t=this.getRelativeXY();return this.group?st(t,this.group.calcTransformMatrix()):t}setXY(t,e,s){this.group&&(t=st(t,xt(this.group.calcTransformMatrix()))),this.setRelativeXY(t,e,s)}getRelativeXY(){return new _(this.left,this.top)}setRelativeXY(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.originX,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.originY;this.setPositionByOrigin(t,e,s)}isStrokeAccountedForInDimensions(){return!1}getCoords(){const{tl:t,tr:e,br:s,bl:r}=this.aCoords||(this.aCoords=this.calcACoords()),i=[t,e,s,r];if(this.group){const n=this.group.calcTransformMatrix();return i.map(o=>st(o,n))}return i}intersectsWithRect(t,e){return X.intersectPolygonRectangle(this.getCoords(),t,e).status==="Intersection"}intersectsWithObject(t){const e=X.intersectPolygonPolygon(this.getCoords(),t.getCoords());return e.status==="Intersection"||e.status==="Coincident"||t.isContainedWithinObject(this)||this.isContainedWithinObject(t)}isContainedWithinObject(t){return this.getCoords().every(e=>t.containsPoint(e))}isContainedWithinRect(t,e){const{left:s,top:r,width:i,height:n}=this.getBoundingRect();return s>=t.x&&s+i<=e.x&&r>=t.y&&r+n<=e.y}isOverlapping(t){return this.intersectsWithObject(t)||this.isContainedWithinObject(t)||t.isContainedWithinObject(this)}containsPoint(t){return X.isPointInPolygon(t,this.getCoords())}isOnScreen(){if(!this.canvas)return!1;const{tl:t,br:e}=this.canvas.vptCoords;return!!this.getCoords().some(s=>s.x<=e.x&&s.x>=t.x&&s.y<=e.y&&s.y>=t.y)||!!this.intersectsWithRect(t,e)||this.containsPoint(t.midPointFrom(e))}isPartiallyOnScreen(){if(!this.canvas)return!1;const{tl:t,br:e}=this.canvas.vptCoords;return this.intersectsWithRect(t,e)?!0:this.getCoords().every(s=>(s.x>=e.x||s.x<=t.x)&&(s.y>=e.y||s.y<=t.y))&&this.containsPoint(t.midPointFrom(e))}getBoundingRect(){return At(this.getCoords())}getScaledWidth(){return this._getTransformedDimensions().x}getScaledHeight(){return this._getTransformedDimensions().y}scale(t){this._set(ot,t),this._set(gt,t),this.setCoords()}scaleToWidth(t){const e=this.getBoundingRect().width/this.getScaledWidth();return this.scale(t/this.width/e)}scaleToHeight(t){const e=this.getBoundingRect().height/this.getScaledHeight();return this.scale(t/this.height/e)}getCanvasRetinaScaling(){var t;return((t=this.canvas)===null||t===void 0?void 0:t.getRetinaScaling())||1}getTotalAngle(){return this.group?It(Ei(this.calcTransformMatrix())):this.angle}getViewportTransform(){var t;return((t=this.canvas)===null||t===void 0?void 0:t.viewportTransform)||tt.concat()}calcACoords(){const t=_e({angle:this.angle}),{x:e,y:s}=this.getRelativeCenterPoint(),r=ts(e,s),i=J(r,t),n=this._getTransformedDimensions(),o=n.x/2,h=n.y/2;return{tl:st({x:-o,y:-h},i),tr:st({x:o,y:-h},i),bl:st({x:-o,y:h},i),br:st({x:o,y:h},i)}}setCoords(){this.aCoords=this.calcACoords()}transformMatrixKey(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=[];return!t&&this.group&&(e=this.group.transformMatrixKey(t)),e.push(this.top,this.left,this.width,this.height,this.scaleX,this.scaleY,this.angle,this.strokeWidth,this.skewX,this.skewY,+this.flipX,+this.flipY,N(this.originX),N(this.originY)),e}calcTransformMatrix(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.calcOwnMatrix();if(t||!this.group)return e;const s=this.transformMatrixKey(t),r=this.matrixCache;return r&&r.key.every((i,n)=>i===s[n])?r.value:(this.group&&(e=J(this.group.calcTransformMatrix(!1),e)),this.matrixCache={key:s,value:e},e)}calcOwnMatrix(){const t=this.transformMatrixKey(!0),e=this.ownMatrixCache;if(e&&e.key===t)return e.value;const s=this.getRelativeCenterPoint(),r={angle:this.angle,translateX:s.x,translateY:s.y,scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY},i=Wn(r);return this.ownMatrixCache={key:t,value:i},i}_getNonTransformedDimensions(){return new _(this.width,this.height).scalarAdd(this.strokeWidth)}_calculateCurrentDimensions(t){return this._getTransformedDimensions(t).transform(this.getViewportTransform(),!0).scalarAdd(2*this.padding)}_getTransformedDimensions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=m({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,width:this.width,height:this.height,strokeWidth:this.strokeWidth},t),s=e.strokeWidth;let r=s,i=0;this.strokeUniform&&(r=0,i=s);const n=e.width+r,o=e.height+r;let h;return h=e.skewX===0&&e.skewY===0?new _(n*e.scaleX,o*e.scaleY):Dr(n,o,Fs(e)),h.scalarAdd(i)}translateToGivenOrigin(t,e,s,r,i){let n=t.x,o=t.y;const h=N(r)-N(e),l=N(i)-N(s);if(h||l){const c=this._getTransformedDimensions();n+=h*c.x,o+=l*c.y}return new _(n,o)}translateToCenterPoint(t,e,s){if(e===k&&s===k)return t;const r=this.translateToGivenOrigin(t,e,s,k,k);return this.angle?r.rotate(H(this.angle),t):r}translateToOriginPoint(t,e,s){const r=this.translateToGivenOrigin(t,k,k,e,s);return this.angle?r.rotate(H(this.angle),t):r}getCenterPoint(){const t=this.getRelativeCenterPoint();return this.group?st(t,this.group.calcTransformMatrix()):t}getRelativeCenterPoint(){return this.translateToCenterPoint(new _(this.left,this.top),this.originX,this.originY)}getPointByOrigin(t,e){return this.translateToOriginPoint(this.getRelativeCenterPoint(),t,e)}setPositionByOrigin(t,e,s){const r=this.translateToCenterPoint(t,e,s),i=this.translateToOriginPoint(r,this.originX,this.originY);this.set({left:i.x,top:i.y})}_getLeftTopCoords(){return this.translateToOriginPoint(this.getRelativeCenterPoint(),j,rt)}}const vo=["type"],yo=["extraParam"];let kt=class ys extends mo{static getDefaults(){return ys.ownDefaults}get type(){const t=this.constructor.type;return t==="FabricObject"?"object":t.toLowerCase()}set type(t){Gt("warn","Setting type has no effect",t)}constructor(t){super(),p(this,"_cacheContext",null),Object.assign(this,ys.ownDefaults),this.setOptions(t)}_createCacheCanvas(){this._cacheCanvas=Bt(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0}_limitCacheSize(t){const e=t.width,s=t.height,r=E.maxCacheSideLimit,i=E.minCacheSideLimit;if(e<=r&&s<=r&&e*s<=E.perfLimitSizeTotal)return e<i&&(t.width=i),s<i&&(t.height=i),t;const n=e/s,[o,h]=ze.limitDimsByArea(n),l=fe(i,o,r),c=fe(i,h,r);return e>l&&(t.zoomX/=e/l,t.width=l,t.capped=!0),s>c&&(t.zoomY/=s/c,t.height=c,t.capped=!0),t}_getCacheCanvasDimensions(){const t=this.getTotalObjectScaling(),e=this._getTransformedDimensions({skewX:0,skewY:0}),s=e.x*t.x/this.scaleX,r=e.y*t.y/this.scaleY;return{width:Math.ceil(s+2),height:Math.ceil(r+2),zoomX:t.x,zoomY:t.y,x:s,y:r}}_updateCacheCanvas(){const t=this._cacheCanvas,e=this._cacheContext,{width:s,height:r,zoomX:i,zoomY:n,x:o,y:h}=this._limitCacheSize(this._getCacheCanvasDimensions()),l=s!==t.width||r!==t.height,c=this.zoomX!==i||this.zoomY!==n;if(!t||!e)return!1;if(l||c){s!==t.width||r!==t.height?(t.width=s,t.height=r):(e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height));const u=o/2,g=h/2;return this.cacheTranslationX=Math.round(t.width/2-u)+u,this.cacheTranslationY=Math.round(t.height/2-g)+g,e.translate(this.cacheTranslationX,this.cacheTranslationY),e.scale(i,n),this.zoomX=i,this.zoomY=n,!0}return!1}setOptions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this._setOptions(t)}transform(t){const e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,s=this.calcTransformMatrix(!e);t.transform(s[0],s[1],s[2],s[3],s[4],s[5])}getObjectScaling(){if(!this.group)return new _(Math.abs(this.scaleX),Math.abs(this.scaleY));const t=ws(this.calcTransformMatrix());return new _(Math.abs(t.scaleX),Math.abs(t.scaleY))}getTotalObjectScaling(){const t=this.getObjectScaling();if(this.canvas){const e=this.canvas.getZoom(),s=this.getCanvasRetinaScaling();return t.scalarMultiply(e*s)}return t}getObjectOpacity(){let t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t}_constrainScale(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:t===0?1e-4:t}_set(t,e){t!==ot&&t!==gt||(e=this._constrainScale(e)),t===ot&&e<0?(this.flipX=!this.flipX,e*=-1):t==="scaleY"&&e<0?(this.flipY=!this.flipY,e*=-1):t!=="shadow"||!e||e instanceof Ft||(e=new Ft(e));const s=this[t]!==e;return this[t]=e,s&&this.constructor.cacheProperties.includes(t)&&(this.dirty=!0),this.parent&&(this.dirty||s&&this.constructor.stateProperties.includes(t))&&this.parent._set("dirty",!0),this}isNotVisible(){return this.opacity===0||!this.width&&!this.height&&this.strokeWidth===0||!this.visible}render(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.drawObject(t,!1,{}),this.dirty=!1),t.restore())}drawSelectionBackground(t){}renderCache(t){if(t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&this._cacheContext){const{zoomX:e,zoomY:s,cacheTranslationX:r,cacheTranslationY:i}=this,{width:n,height:o}=this._cacheCanvas;this.drawObject(this._cacheContext,t.forClipping,{zoomX:e,zoomY:s,cacheTranslationX:r,cacheTranslationY:i,width:n,height:o,parentClipPaths:[]}),this.dirty=!1}}_removeCacheCanvas(){this._cacheCanvas=void 0,this._cacheContext=null}hasStroke(){return this.stroke&&this.stroke!=="transparent"&&this.strokeWidth!==0}hasFill(){return this.fill&&this.fill!=="transparent"}needsItsOwnCache(){return!!(this.paintFirst===nt&&this.hasFill()&&this.hasStroke()&&this.shadow)||!!this.clipPath}shouldCache(){return this.ownCaching=this.objectCaching&&(!this.parent||!this.parent.isOnACache())||this.needsItsOwnCache(),this.ownCaching}willDrawShadow(){return!!this.shadow&&(this.shadow.offsetX!==0||this.shadow.offsetY!==0)}drawClipPathOnCache(t,e,s){t.save(),e.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",t.setTransform(1,0,0,1,0,0),t.drawImage(s,0,0),t.restore()}drawObject(t,e,s){const r=this.fill,i=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath,s),this.fill=r,this.stroke=i}createClipPathLayer(t,e){const s=dt(e),r=s.getContext("2d");if(r.translate(e.cacheTranslationX,e.cacheTranslationY),r.scale(e.zoomX,e.zoomY),t._cacheCanvas=s,e.parentClipPaths.forEach(i=>{i.transform(r)}),e.parentClipPaths.push(t),t.absolutePositioned){const i=xt(this.calcTransformMatrix());r.transform(i[0],i[1],i[2],i[3],i[4],i[5])}return t.transform(r),t.drawObject(r,!0,e),s}_drawClipPath(t,e,s){if(!e)return;e._transformDone=!0;const r=this.createClipPathLayer(e,s);this.drawClipPathOnCache(t,e,r)}drawCacheOnCanvas(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)}isCacheDirty(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0];if(this.isNotVisible())return!1;const e=this._cacheCanvas,s=this._cacheContext;return!(!e||!s||t||!this._updateCacheCanvas())||!!(this.dirty||this.clipPath&&this.clipPath.absolutePositioned)&&(e&&s&&!t&&(s.save(),s.setTransform(1,0,0,1,0,0),s.clearRect(0,0,e.width,e.height),s.restore()),!0)}_renderBackground(t){if(!this.backgroundColor)return;const e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}_setOpacity(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity}_setStrokeStyles(t,e){const s=e.stroke;s&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,ut(s)?s.gradientUnits==="percentage"||s.gradientTransform||s.patternTransform?this._applyPatternForTransformedGradient(t,s):(t.strokeStyle=s.toLive(t),this._applyPatternGradientTransform(t,s)):t.strokeStyle=e.stroke)}_setFillStyles(t,e){let{fill:s}=e;s&&(ut(s)?(t.fillStyle=s.toLive(t),this._applyPatternGradientTransform(t,s)):t.fillStyle=s)}_setClippingProperties(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"}_setLineDash(t,e){e&&e.length!==0&&t.setLineDash(e)}_setShadow(t){if(!this.shadow)return;const e=this.shadow,s=this.canvas,r=this.getCanvasRetinaScaling(),[i,,,n]=(s==null?void 0:s.viewportTransform)||tt,o=i*r,h=n*r,l=e.nonScaling?new _(1,1):this.getObjectScaling();t.shadowColor=e.color,t.shadowBlur=e.blur*E.browserShadowBlurConstant*(o+h)*(l.x+l.y)/4,t.shadowOffsetX=e.offsetX*o*l.x,t.shadowOffsetY=e.offsetY*h*l.y}_removeShadow(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)}_applyPatternGradientTransform(t,e){if(!ut(e))return{offsetX:0,offsetY:0};const s=e.gradientTransform||e.patternTransform,r=-this.width/2+e.offsetX||0,i=-this.height/2+e.offsetY||0;return e.gradientUnits==="percentage"?t.transform(this.width,0,0,this.height,r,i):t.transform(1,0,0,1,r,i),s&&t.transform(s[0],s[1],s[2],s[3],s[4],s[5]),{offsetX:r,offsetY:i}}_renderPaintInOrder(t){this.paintFirst===nt?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))}_render(t){}_renderFill(t){this.fill&&(t.save(),this._setFillStyles(t,this),this.fillRule==="evenodd"?t.fill("evenodd"):t.fill(),t.restore())}_renderStroke(t){if(this.stroke&&this.strokeWidth!==0){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform){const e=this.getObjectScaling();t.scale(1/e.x,1/e.y)}this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}}_applyPatternForTransformedGradient(t,e){var s;const r=this._limitCacheSize(this._getCacheCanvasDimensions()),i=this.getCanvasRetinaScaling(),n=r.x/this.scaleX/i,o=r.y/this.scaleY/i,h=dt({width:Math.ceil(n),height:Math.ceil(o)}),l=h.getContext("2d");l&&(l.beginPath(),l.moveTo(0,0),l.lineTo(n,0),l.lineTo(n,o),l.lineTo(0,o),l.closePath(),l.translate(n/2,o/2),l.scale(r.zoomX/this.scaleX/i,r.zoomY/this.scaleY/i),this._applyPatternGradientTransform(l,e),l.fillStyle=e.toLive(t),l.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(i*this.scaleX/r.zoomX,i*this.scaleY/r.zoomY),t.strokeStyle=(s=l.createPattern(h,"no-repeat"))!==null&&s!==void 0?s:"")}_findCenterFromElement(){return new _(this.left+this.width/2,this.top+this.height/2)}clone(t){const e=this.toObject(t);return this.constructor.fromObject(e)}cloneAsImage(t){const e=this.toCanvasElement(t);return new(T.getClass("image"))(e)}toCanvasElement(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=Ii(this),s=this.group,r=this.shadow,i=Math.abs,n=t.enableRetinaScaling?bi():1,o=(t.multiplier||1)*n,h=t.canvasProvider||(C=>new es(C,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1}));delete this.group,t.withoutTransform&&Nn(this),t.withoutShadow&&(this.shadow=null),t.viewportTransform&&qn(this,this.getViewportTransform()),this.setCoords();const l=Bt(),c=this.getBoundingRect(),u=this.shadow,g=new _;if(u){const C=u.blur,w=u.nonScaling?new _(1,1):this.getObjectScaling();g.x=2*Math.round(i(u.offsetX)+C)*i(w.x),g.y=2*Math.round(i(u.offsetY)+C)*i(w.y)}const d=c.width+g.x,f=c.height+g.y;l.width=Math.ceil(d),l.height=Math.ceil(f);const v=h(l);t.format==="jpeg"&&(v.backgroundColor="#fff"),this.setPositionByOrigin(new _(v.width/2,v.height/2),k,k);const y=this.canvas;v._objects=[this],this.set("canvas",v),this.setCoords();const x=v.toCanvasElement(o||1,t);return this.set("canvas",y),this.shadow=r,s&&(this.group=s),this.set(e),this.setCoords(),v._objects=[],v.destroy(),x}toDataURL(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return ki(this.toCanvasElement(t),t.format||"png",t.quality||1)}toBlob(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Mi(this.toCanvasElement(t),t.format||"png",t.quality||1)}isType(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return e.includes(this.constructor.type)||e.includes(this.type)}complexity(){return 1}toJSON(){return this.toObject()}rotate(t){const{centeredRotation:e,originX:s,originY:r}=this;if(e){const{x:i,y:n}=this.getRelativeCenterPoint();this.originX=k,this.originY=k,this.left=i,this.top=n}if(this.set("angle",t),e){const{x:i,y:n}=this.translateToOriginPoint(this.getRelativeCenterPoint(),s,r);this.left=i,this.top=n,this.originX=s,this.originY=r}}setOnGroup(){}_setupCompositeOperation(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)}dispose(){Ss.cancelByTarget(this),this.off(),this._set("canvas",void 0),this._cacheCanvas&&Ot().dispose(this._cacheCanvas),this._cacheCanvas=void 0,this._cacheContext=null}animate(t,e){return Object.entries(t).reduce((s,r)=>{let[i,n]=r;return s[i]=this._animate(i,n,e),s},{})}_animate(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const r=t.split("."),i=this.constructor.colorProperties.includes(r[r.length-1]),{abort:n,startValue:o,onChange:h,onComplete:l}=s,c=m(m({},s),{},{target:this,startValue:o??r.reduce((u,g)=>u[g],this),endValue:e,abort:n==null?void 0:n.bind(this),onChange:(u,g,d)=>{r.reduce((f,v,y)=>(y===r.length-1&&(f[v]=u),f[v]),this),h&&h(u,g,d)},onComplete:(u,g,d)=>{this.setCoords(),l&&l(u,g,d)}});return i?po(c):Ui(c)}isDescendantOf(t){const{parent:e,group:s}=this;return e===t||s===t||!!e&&e.isDescendantOf(t)||!!s&&s!==e&&s.isDescendantOf(t)}getAncestors(){const t=[];let e=this;do e=e.parent,e&&t.push(e);while(e);return t}findCommonAncestors(t){if(this===t)return{fork:[],otherFork:[],common:[this,...this.getAncestors()]};const e=this.getAncestors(),s=t.getAncestors();if(e.length===0&&s.length>0&&this===s[s.length-1])return{fork:[],otherFork:[t,...s.slice(0,s.length-1)],common:[this]};for(let r,i=0;i<e.length;i++){if(r=e[i],r===t)return{fork:[this,...e.slice(0,i)],otherFork:[],common:e.slice(i)};for(let n=0;n<s.length;n++){if(this===s[n])return{fork:[],otherFork:[t,...s.slice(0,n)],common:[this,...e]};if(r===s[n])return{fork:[this,...e.slice(0,i)],otherFork:[t,...s.slice(0,n)],common:e.slice(i)}}}return{fork:[this,...e],otherFork:[t,...s],common:[]}}hasCommonAncestors(t){const e=this.findCommonAncestors(t);return e&&!!e.common.length}isInFrontOf(t){if(this===t)return;const e=this.findCommonAncestors(t);if(e.fork.includes(t))return!0;if(e.otherFork.includes(this))return!1;const s=e.common[0]||this.canvas;if(!s)return;const r=e.fork.pop(),i=e.otherFork.pop(),n=s._objects.indexOf(r),o=s._objects.indexOf(i);return n>-1&&n>o}toObject(){const t=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).concat(ys.customProperties,this.constructor.customProperties||[]);let e;const s=E.NUM_FRACTION_DIGITS,{clipPath:r,fill:i,stroke:n,shadow:o,strokeDashArray:h,left:l,top:c,originX:u,originY:g,width:d,height:f,strokeWidth:v,strokeLineCap:y,strokeDashOffset:x,strokeLineJoin:C,strokeUniform:w,strokeMiterLimit:S,scaleX:b,scaleY:D,angle:M,flipX:O,flipY:F,opacity:L,visible:q,backgroundColor:V,fillRule:P,paintFirst:W,globalCompositeOperation:at,skewX:et,skewY:ft}=this;r&&!r.excludeFromExport&&(e=r.toObject(t.concat("inverted","absolutePositioned")));const Y=Dt=>B(Dt,s),bt=m(m({},xe(this,t)),{},{type:this.constructor.type,version:nr,originX:u,originY:g,left:Y(l),top:Y(c),width:Y(d),height:Y(f),fill:Yr(i)?i.toObject():i,stroke:Yr(n)?n.toObject():n,strokeWidth:Y(v),strokeDashArray:h&&h.concat(),strokeLineCap:y,strokeDashOffset:x,strokeLineJoin:C,strokeUniform:w,strokeMiterLimit:Y(S),scaleX:Y(b),scaleY:Y(D),angle:Y(M),flipX:O,flipY:F,opacity:Y(L),shadow:o&&o.toObject(),visible:q,backgroundColor:V,fillRule:P,paintFirst:W,globalCompositeOperation:at,skewX:Y(et),skewY:Y(ft)},e?{clipPath:e}:null);return this.includeDefaultValues?bt:this._removeDefaultValues(bt)}toDatalessObject(t){return this.toObject(t)}_removeDefaultValues(t){const e=this.constructor.getDefaults(),s=Object.keys(e).length>0?e:Object.getPrototypeOf(this);return Or(t,(r,i)=>{if(i===j||i===rt||i==="type")return!0;const n=s[i];return r!==n&&!(Array.isArray(r)&&Array.isArray(n)&&r.length===0&&n.length===0)})}toString(){return"#<".concat(this.constructor.type,">")}static _fromObject(t){let e=I(t,vo),s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{extraParam:r}=s,i=I(s,yo);return Ls(e,i).then(n=>r?(delete n[r],new this(e[r],n)):new this(n))}static fromObject(t,e){return this._fromObject(t,e)}};p(kt,"stateProperties",ro),p(kt,"cacheProperties",Xt),p(kt,"ownDefaults",io),p(kt,"type","FabricObject"),p(kt,"colorProperties",[K,nt,"backgroundColor"]),p(kt,"customProperties",[]),T.setClass(kt),T.setClass(kt,"object");const Ce=(a,t,e)=>(s,r,i,n)=>{const o=t(s,r,i,n);return o&&Xi(a,m(m({},Yi(s,r,i,n)),e)),o};function be(a){return(t,e,s,r)=>{const{target:i,originX:n,originY:o}=e,h=i.getRelativeCenterPoint(),l=i.translateToOriginPoint(h,n,o),c=a(t,e,s,r);return i.setPositionByOrigin(l,e.originX,e.originY),c}}const ei=Ce(Ue,be((a,t,e,s)=>{const r=kr(t,t.originX,t.originY,e,s);if(N(t.originX)===N(k)||N(t.originX)===N(z)&&r.x<0||N(t.originX)===N(j)&&r.x>0){const{target:i}=t,n=i.strokeWidth/(i.strokeUniform?i.scaleX:1),o=Wi(t)?2:1,h=i.width,l=Math.abs(r.x*o/i.scaleX)-n;return i.set("width",Math.max(l,1)),h!==i.width}return!1}));function _o(a,t,e,s,r){s=s||{};const i=this.sizeX||s.cornerSize||r.cornerSize,n=this.sizeY||s.cornerSize||r.cornerSize,o=s.transparentCorners!==void 0?s.transparentCorners:r.transparentCorners,h=o?nt:K,l=!o&&(s.cornerStrokeColor||r.cornerStrokeColor);let c,u=t,g=e;a.save(),a.fillStyle=s.cornerColor||r.cornerColor||"",a.strokeStyle=s.cornerStrokeColor||r.cornerStrokeColor||"",i>n?(c=i,a.scale(1,n/i),g=e*i/n):n>i?(c=n,a.scale(i/n,1),u=t*n/i):c=i,a.beginPath(),a.arc(u,g,c/2,0,xs,!1),a[h](),l&&a.stroke(),a.restore()}function xo(a,t,e,s,r){s=s||{};const i=this.sizeX||s.cornerSize||r.cornerSize,n=this.sizeY||s.cornerSize||r.cornerSize,o=s.transparentCorners!==void 0?s.transparentCorners:r.transparentCorners,h=o?nt:K,l=!o&&(s.cornerStrokeColor||r.cornerStrokeColor),c=i/2,u=n/2;a.save(),a.fillStyle=s.cornerColor||r.cornerColor||"",a.strokeStyle=s.cornerStrokeColor||r.cornerStrokeColor||"",a.translate(t,e);const g=r.getTotalAngle();a.rotate(H(g)),a["".concat(h,"Rect")](-c,-u,i,n),l&&a.strokeRect(-c,-u,i,n),a.restore()}class vt{constructor(t){p(this,"visible",!0),p(this,"actionName",As),p(this,"angle",0),p(this,"x",0),p(this,"y",0),p(this,"offsetX",0),p(this,"offsetY",0),p(this,"sizeX",0),p(this,"sizeY",0),p(this,"touchSizeX",0),p(this,"touchSizeY",0),p(this,"cursorStyle","crosshair"),p(this,"withConnection",!1),Object.assign(this,t)}shouldActivate(t,e,s,r){var i;let{tl:n,tr:o,br:h,bl:l}=r;return((i=e.canvas)===null||i===void 0?void 0:i.getActiveObject())===e&&e.isControlVisible(t)&&X.isPointInPolygon(s,[n,o,h,l])}getActionHandler(t,e,s){return this.actionHandler}getMouseDownHandler(t,e,s){return this.mouseDownHandler}getMouseUpHandler(t,e,s){return this.mouseUpHandler}cursorStyleHandler(t,e,s){return e.cursorStyle}getActionName(t,e,s){return e.actionName}getVisibility(t,e){var s,r;return(s=(r=t._controlsVisibility)===null||r===void 0?void 0:r[e])!==null&&s!==void 0?s:this.visible}setVisibility(t,e,s){this.visible=t}positionHandler(t,e,s,r){return new _(this.x*t.x+this.offsetX,this.y*t.y+this.offsetY).transform(e)}calcCornerCoords(t,e,s,r,i,n){const o=wr([ts(s,r),_e({angle:t}),Tr((i?this.touchSizeX:this.sizeX)||e,(i?this.touchSizeY:this.sizeY)||e)]);return{tl:new _(-.5,-.5).transform(o),tr:new _(.5,-.5).transform(o),br:new _(.5,.5).transform(o),bl:new _(-.5,.5).transform(o)}}render(t,e,s,r,i){((r=r||{}).cornerStyle||i.cornerStyle)==="circle"?_o.call(this,t,e,s,r,i):xo.call(this,t,e,s,r,i)}}const Co=(a,t,e)=>e.lockRotation?Os:t.cursorStyle,bo=Ce(wi,be((a,t,e,s)=>{let{target:r,ex:i,ey:n,theta:o,originX:h,originY:l}=t;const c=r.translateToOriginPoint(r.getRelativeCenterPoint(),h,l);if(Ct(r,"lockRotation"))return!1;const u=Math.atan2(n-c.y,i-c.x),g=Math.atan2(s-c.y,e-c.x);let d=It(g-u+o);if(r.snapAngle&&r.snapAngle>0){const v=r.snapAngle,y=r.snapThreshold||v,x=Math.ceil(d/v)*v,C=Math.floor(d/v)*v;Math.abs(d-C)<y?d=C:Math.abs(d-x)<y&&(d=x)}d<0&&(d=360+d),d%=360;const f=r.angle!==d;return r.angle=d,f}));function qi(a,t){const e=t.canvas,s=a[e.uniScaleKey];return e.uniformScaling&&!s||!e.uniformScaling&&s}function Ki(a,t,e){const s=Ct(a,"lockScalingX"),r=Ct(a,"lockScalingY");if(s&&r||!t&&(s||r)&&e||s&&t==="x"||r&&t==="y")return!0;const{width:i,height:n,strokeWidth:o}=a;return i===0&&o===0&&t!=="y"||n===0&&o===0&&t!=="x"}const So=["e","se","s","sw","w","nw","n","ne","e"],De=(a,t,e)=>{const s=qi(a,e);if(Ki(e,t.x!==0&&t.y===0?"x":t.x===0&&t.y!==0?"y":"",s))return Os;const r=Vi(e,t);return"".concat(So[r],"-resize")};function jr(a,t,e,s){let r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{};const i=t.target,n=r.by,o=qi(a,i);let h,l,c,u,g,d;if(Ki(i,n,o))return!1;if(t.gestureScale)l=t.scaleX*t.gestureScale,c=t.scaleY*t.gestureScale;else{if(h=kr(t,t.originX,t.originY,e,s),g=n!=="y"?Math.sign(h.x||t.signX||1):1,d=n!=="x"?Math.sign(h.y||t.signY||1):1,t.signX||(t.signX=g),t.signY||(t.signY=d),Ct(i,"lockScalingFlip")&&(t.signX!==g||t.signY!==d))return!1;if(u=i._getTransformedDimensions(),o&&!n){const y=Math.abs(h.x)+Math.abs(h.y),{original:x}=t,C=y/(Math.abs(u.x*x.scaleX/i.scaleX)+Math.abs(u.y*x.scaleY/i.scaleY));l=x.scaleX*C,c=x.scaleY*C}else l=Math.abs(h.x*i.scaleX/u.x),c=Math.abs(h.y*i.scaleY/u.y);Wi(t)&&(l*=2,c*=2),t.signX!==g&&n!=="y"&&(t.originX=Gr(t.originX),l*=-1,t.signX=g),t.signY!==d&&n!=="x"&&(t.originY=Gr(t.originY),c*=-1,t.signY=d)}const f=i.scaleX,v=i.scaleY;return n?(n==="x"&&i.set(ot,l),n==="y"&&i.set(gt,c)):(!Ct(i,"lockScalingX")&&i.set(ot,l),!Ct(i,"lockScalingY")&&i.set(gt,c)),f!==i.scaleX||v!==i.scaleY}const ns=Ce(Ps,be((a,t,e,s)=>jr(a,t,e,s))),wo=Ce(Ps,be((a,t,e,s)=>jr(a,t,e,s,{by:"x"}))),To=Ce(Ps,be((a,t,e,s)=>jr(a,t,e,s,{by:"y"}))),Oo=["target","ex","ey","skewingSide"],Ks={x:{counterAxis:"y",scale:ot,skew:ve,lockSkewing:"lockSkewingX",origin:"originX",flip:"flipX"},y:{counterAxis:"x",scale:gt,skew:ye,lockSkewing:"lockSkewingY",origin:"originY",flip:"flipY"}},Do=["ns","nesw","ew","nwse"],ko=(a,t,e)=>{if(t.x!==0&&Ct(e,"lockSkewingY")||t.y!==0&&Ct(e,"lockSkewingX"))return Os;const s=Vi(e,t)%4;return"".concat(Do[s],"-resize")};function Ji(a,t,e,s,r){const{target:i}=e,{counterAxis:n,origin:o,lockSkewing:h,skew:l,flip:c}=Ks[a];if(Ct(i,h))return!1;const{origin:u,flip:g}=Ks[n],d=N(e[u])*(i[g]?-1:1),f=-Math.sign(d)*(i[c]?-1:1),v=.5*-((i[l]===0&&kr(e,k,k,s,r)[a]>0||i[l]>0?1:-1)*f)+.5;return Ce(Ti,be((x,C,w,S)=>function(b,D,M){let{target:O,ex:F,ey:L,skewingSide:q}=D,V=I(D,Oo);const{skew:P}=Ks[b],W=M.subtract(new _(F,L)).divide(new _(O.scaleX,O.scaleY))[b],at=O[P],et=V[P],ft=Math.tan(H(et)),Y=b==="y"?O._getTransformedDimensions({scaleX:1,scaleY:1,skewX:0}).x:O._getTransformedDimensions({scaleX:1,scaleY:1}).y,bt=2*W*q/Math.max(Y,1)+ft,Dt=It(Math.atan(bt));O.set(P,Dt);const ss=at!==O[P];if(ss&&b==="y"){const{skewX:Vs,scaleX:Se}=O,Yt=O._getTransformedDimensions({skewY:at}),rs=O._getTransformedDimensions(),Kt=Vs!==0?Yt.x/rs.x:1;Kt!==1&&O.set(ot,Kt*Se)}return ss}(a,C,new _(w,S))))(t,m(m({},e),{},{[o]:v,skewingSide:f}),s,r)}const Mo=(a,t,e,s)=>Ji("x",a,t,e,s),Eo=(a,t,e,s)=>Ji("y",a,t,e,s);function Is(a,t){return a[t.canvas.altActionKey]}const os=(a,t,e)=>{const s=Is(a,e);return t.x===0?s?ve:gt:t.y===0?s?ye:ot:""},le=(a,t,e)=>Is(a,e)?ko(0,t,e):De(a,t,e),si=(a,t,e,s)=>Is(a,t.target)?Eo(a,t,e,s):wo(a,t,e,s),ri=(a,t,e,s)=>Is(a,t.target)?Mo(a,t,e,s):To(a,t,e,s),Zi=()=>({ml:new vt({x:-.5,y:0,cursorStyleHandler:le,actionHandler:si,getActionName:os}),mr:new vt({x:.5,y:0,cursorStyleHandler:le,actionHandler:si,getActionName:os}),mb:new vt({x:0,y:.5,cursorStyleHandler:le,actionHandler:ri,getActionName:os}),mt:new vt({x:0,y:-.5,cursorStyleHandler:le,actionHandler:ri,getActionName:os}),tl:new vt({x:-.5,y:-.5,cursorStyleHandler:De,actionHandler:ns}),tr:new vt({x:.5,y:-.5,cursorStyleHandler:De,actionHandler:ns}),bl:new vt({x:-.5,y:.5,cursorStyleHandler:De,actionHandler:ns}),br:new vt({x:.5,y:.5,cursorStyleHandler:De,actionHandler:ns}),mtr:new vt({x:0,y:-.5,actionHandler:bo,cursorStyleHandler:Co,offsetY:-40,withConnection:!0,actionName:br})}),jo=()=>({mr:new vt({x:.5,y:0,actionHandler:ei,cursorStyleHandler:le,actionName:Ue}),ml:new vt({x:-.5,y:0,actionHandler:ei,cursorStyleHandler:le,actionName:Ue})}),Po=()=>m(m({},Zi()),jo());class Ze extends kt{static getDefaults(){return m(m({},super.getDefaults()),Ze.ownDefaults)}constructor(t){super(),Object.assign(this,this.constructor.createControls(),Ze.ownDefaults),this.setOptions(t)}static createControls(){return{controls:Zi()}}_updateCacheCanvas(){const t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){const e=t._currentTransform,s=e.target,r=e.action;if(this===s&&r&&r.startsWith(As))return!1}return super._updateCacheCanvas()}getActiveControl(){const t=this.__corner;return t?{key:t,control:this.controls[t],coord:this.oCoords[t]}:void 0}findControl(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];if(!this.hasControls||!this.canvas)return;this.__corner=void 0;const s=Object.entries(this.oCoords);for(let r=s.length-1;r>=0;r--){const[i,n]=s[r],o=this.controls[i];if(o.shouldActivate(i,this,t,e?n.touchCorner:n.corner))return this.__corner=i,{key:i,control:o,coord:this.oCoords[i]}}}calcOCoords(){const t=this.getViewportTransform(),e=this.getCenterPoint(),s=ts(e.x,e.y),r=_e({angle:this.getTotalAngle()-(this.group&&this.flipX?180:0)}),i=J(s,r),n=J(t,i),o=J(n,[1/t[0],0,0,1/t[3],0,0]),h=this.group?ws(this.calcTransformMatrix()):void 0;h&&(h.scaleX=Math.abs(h.scaleX),h.scaleY=Math.abs(h.scaleY));const l=this._calculateCurrentDimensions(h),c={};return this.forEachControl((u,g)=>{const d=u.positionHandler(l,o,this,u);c[g]=Object.assign(d,this._calcCornerCoords(u,d))}),c}_calcCornerCoords(t,e){const s=this.getTotalAngle();return{corner:t.calcCornerCoords(s,this.cornerSize,e.x,e.y,!1,this),touchCorner:t.calcCornerCoords(s,this.touchCornerSize,e.x,e.y,!0,this)}}setCoords(){super.setCoords(),this.canvas&&(this.oCoords=this.calcOCoords())}forEachControl(t){for(const e in this.controls)t(this.controls[e],e,this)}drawSelectionBackground(t){if(!this.selectionBackgroundColor||this.canvas&&this.canvas._activeObject!==this)return;t.save();const e=this.getRelativeCenterPoint(),s=this._calculateCurrentDimensions(),r=this.getViewportTransform();t.translate(e.x,e.y),t.scale(1/r[0],1/r[3]),t.rotate(H(this.angle)),t.fillStyle=this.selectionBackgroundColor,t.fillRect(-s.x/2,-s.y/2,s.x,s.y),t.restore()}strokeBorders(t,e){t.strokeRect(-e.x/2,-e.y/2,e.x,e.y)}_drawBorders(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const r=m({hasControls:this.hasControls,borderColor:this.borderColor,borderDashArray:this.borderDashArray},s);t.save(),t.strokeStyle=r.borderColor,this._setLineDash(t,r.borderDashArray),this.strokeBorders(t,e),r.hasControls&&this.drawControlsConnectingLines(t,e),t.restore()}_renderControls(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{hasBorders:s,hasControls:r}=this,i=m({hasBorders:s,hasControls:r},e),n=this.getViewportTransform(),o=i.hasBorders,h=i.hasControls,l=J(n,this.calcTransformMatrix()),c=ws(l);t.save(),t.translate(c.translateX,c.translateY),t.lineWidth=this.borderScaleFactor,this.group===this.parent&&(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(c.angle-=180),t.rotate(H(this.group?c.angle:this.angle)),o&&this.drawBorders(t,c,e),h&&this.drawControls(t,e),t.restore()}drawBorders(t,e,s){let r;if(s&&s.forActiveSelection||this.group){const i=Dr(this.width,this.height,Fs(e)),n=this.isStrokeAccountedForInDimensions()?Sr:(this.strokeUniform?new _().scalarAdd(this.canvas?this.canvas.getZoom():1):new _(e.scaleX,e.scaleY)).scalarMultiply(this.strokeWidth);r=i.add(n).scalarAdd(this.borderScaleFactor).scalarAdd(2*this.padding)}else r=this._calculateCurrentDimensions().scalarAdd(this.borderScaleFactor);this._drawBorders(t,r,s)}drawControlsConnectingLines(t,e){let s=!1;t.beginPath(),this.forEachControl((r,i)=>{r.withConnection&&r.getVisibility(this,i)&&(s=!0,t.moveTo(r.x*e.x,r.y*e.y),t.lineTo(r.x*e.x+r.offsetX,r.y*e.y+r.offsetY))}),s&&t.stroke()}drawControls(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};t.save();const s=this.getCanvasRetinaScaling(),{cornerStrokeColor:r,cornerDashArray:i,cornerColor:n}=this,o=m({cornerStrokeColor:r,cornerDashArray:i,cornerColor:n},e);t.setTransform(s,0,0,s,0,0),t.strokeStyle=t.fillStyle=o.cornerColor,this.transparentCorners||(t.strokeStyle=o.cornerStrokeColor),this._setLineDash(t,o.cornerDashArray),this.forEachControl((h,l)=>{if(h.getVisibility(this,l)){const c=this.oCoords[l];h.render(t,c.x,c.y,o,this)}}),t.restore()}isControlVisible(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)}setControlVisible(t,e){this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e}setControlsVisibility(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Object.entries(t).forEach(e=>{let[s,r]=e;return this.setControlVisible(s,r)})}clearContextTop(t){if(!this.canvas)return;const e=this.canvas.contextTop;if(!e)return;const s=this.canvas.viewportTransform;e.save(),e.transform(s[0],s[1],s[2],s[3],s[4],s[5]),this.transform(e);const r=this.width+4,i=this.height+4;return e.clearRect(-r/2,-i/2,r,i),t||e.restore(),e}onDeselect(t){return!1}onSelect(t){return!1}shouldStartDragging(t){return!1}onDragStart(t){return!1}canDrop(t){return!1}renderDragSourceEffect(t){}renderDropTargetEffect(t){}}function $i(a,t){return t.forEach(e=>{Object.getOwnPropertyNames(e.prototype).forEach(s=>{s!=="constructor"&&Object.defineProperty(a.prototype,s,Object.getOwnPropertyDescriptor(e.prototype,s)||Object.create(null))})}),a}p(Ze,"ownDefaults",{noScaleCache:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,cornerSize:13,touchCornerSize:24,transparentCorners:!0,cornerColor:"rgb(178,204,255)",cornerStrokeColor:"",cornerStyle:"rect",cornerDashArray:null,hasControls:!0,borderColor:"rgb(178,204,255)",borderDashArray:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,hasBorders:!0,selectionBackgroundColor:"",selectable:!0,evented:!0,perPixelTargetFind:!1,activeOn:"down",hoverCursor:null,moveCursor:null});class Z extends Ze{}$i(Z,[zi]),T.setClass(Z),T.setClass(Z,"object");const Ao=(a,t,e,s)=>{const r=2*(s=Math.round(s))+1,{data:i}=a.getImageData(t-s,e-s,r,r);for(let n=3;n<i.length;n+=4)if(i[n]>0)return!1;return!0};class Qi{constructor(t){this.options=t,this.strokeProjectionMagnitude=this.options.strokeWidth/2,this.scale=new _(this.options.scaleX,this.options.scaleY),this.strokeUniformScalar=this.options.strokeUniform?new _(1/this.options.scaleX,1/this.options.scaleY):new _(1,1)}createSideVector(t,e){const s=lr(t,e);return this.options.strokeUniform?s.multiply(this.scale):s}projectOrthogonally(t,e,s){return this.applySkew(t.add(this.calcOrthogonalProjection(t,e,s)))}isSkewed(){return this.options.skewX!==0||this.options.skewY!==0}applySkew(t){const e=new _(t);return e.y+=e.x*Math.tan(H(this.options.skewY)),e.x+=e.y*Math.tan(H(this.options.skewX)),e}scaleUnitVector(t,e){return t.multiply(this.strokeUniformScalar).scalarMultiply(e)}}const Fo=new _;class de extends Qi{static getOrthogonalRotationFactor(t,e){const s=e?ur(t,e):eo(t);return Math.abs(s)<Qe?-1:1}constructor(t,e,s,r){super(r),p(this,"AB",void 0),p(this,"AC",void 0),p(this,"alpha",void 0),p(this,"bisector",void 0),this.A=new _(t),this.B=new _(e),this.C=new _(s),this.AB=this.createSideVector(this.A,this.B),this.AC=this.createSideVector(this.A,this.C),this.alpha=ur(this.AB,this.AC),this.bisector=Mr(Gi(this.AB.eq(Fo)?this.AC:this.AB,this.alpha/2))}calcOrthogonalProjection(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.strokeProjectionMagnitude;const r=this.createSideVector(t,e),i=Ni(r),n=de.getOrthogonalRotationFactor(i,this.bisector);return this.scaleUnitVector(i,s*n)}projectBevel(){const t=[];return(this.alpha%xs==0?[this.B]:[this.B,this.C]).forEach(e=>{t.push(this.projectOrthogonally(this.A,e)),t.push(this.projectOrthogonally(this.A,e,-this.strokeProjectionMagnitude))}),t}projectMiter(){const t=[],e=Math.abs(this.alpha),s=1/Math.sin(e/2),r=this.scaleUnitVector(this.bisector,-this.strokeProjectionMagnitude*s),i=this.options.strokeUniform?cr(this.scaleUnitVector(this.bisector,this.options.strokeMiterLimit)):this.options.strokeMiterLimit;return cr(r)/this.strokeProjectionMagnitude<=i&&t.push(this.applySkew(this.A.add(r))),t.push(...this.projectBevel()),t}projectRoundNoSkew(t,e){const s=[],r=new _(de.getOrthogonalRotationFactor(this.bisector),de.getOrthogonalRotationFactor(new _(this.bisector.y,this.bisector.x)));return[new _(1,0).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(r),new _(0,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(r)].forEach(i=>{$r(i,t,e)&&s.push(this.A.add(i))}),s}projectRoundWithSkew(t,e){const s=[],{skewX:r,skewY:i,scaleX:n,scaleY:o,strokeUniform:h}=this.options,l=new _(Math.tan(H(r)),Math.tan(H(i))),c=this.strokeProjectionMagnitude,u=h?c/o/Math.sqrt(1/o**2+1/n**2*l.y**2):c/Math.sqrt(1+l.y**2),g=new _(Math.sqrt(Math.max(c**2-u**2,0)),u),d=h?c/Math.sqrt(1+l.x**2*(1/o)**2/(1/n+1/n*l.x*l.y)**2):c/Math.sqrt(1+l.x**2/(1+l.x*l.y)**2),f=new _(d,Math.sqrt(Math.max(c**2-d**2,0)));return[f,f.scalarMultiply(-1),g,g.scalarMultiply(-1)].map(v=>this.applySkew(h?v.multiply(this.strokeUniformScalar):v)).forEach(v=>{$r(v,t,e)&&s.push(this.applySkew(this.A).add(v))}),s}projectRound(){const t=[];t.push(...this.projectBevel());const e=this.alpha%xs==0,s=this.applySkew(this.A),r=t[e?0:2].subtract(s),i=t[e?1:0].subtract(s),n=e?this.applySkew(this.AB.scalarMultiply(-1)):this.applySkew(this.bisector.multiply(this.strokeUniformScalar).scalarMultiply(-1)),o=He(r,n)>0,h=o?r:i,l=o?i:r;return this.isSkewed()?t.push(...this.projectRoundWithSkew(h,l)):t.push(...this.projectRoundNoSkew(h,l)),t}projectPoints(){switch(this.options.strokeLineJoin){case"miter":return this.projectMiter();case"round":return this.projectRound();default:return this.projectBevel()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t,angle:this.alpha,bisector:this.bisector}))}}class ii extends Qi{constructor(t,e,s){super(s),this.A=new _(t),this.T=new _(e)}calcOrthogonalProjection(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.strokeProjectionMagnitude;const r=this.createSideVector(t,e);return this.scaleUnitVector(Ni(r),s)}projectButt(){return[this.projectOrthogonally(this.A,this.T,this.strokeProjectionMagnitude),this.projectOrthogonally(this.A,this.T,-this.strokeProjectionMagnitude)]}projectRound(){const t=[];if(!this.isSkewed()&&this.A.eq(this.T)){const e=new _(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.applySkew(this.A.add(e)),this.applySkew(this.A.subtract(e)))}else t.push(...new de(this.A,this.T,this.T,this.options).projectRound());return t}projectSquare(){const t=[];if(this.A.eq(this.T)){const e=new _(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.A.add(e),this.A.subtract(e))}else{const e=this.calcOrthogonalProjection(this.A,this.T,this.strokeProjectionMagnitude),s=this.scaleUnitVector(Mr(this.createSideVector(this.A,this.T)),-this.strokeProjectionMagnitude),r=this.A.add(s);t.push(r.add(e),r.subtract(e))}return t.map(e=>this.applySkew(e))}projectPoints(){switch(this.options.strokeLineCap){case"round":return this.projectRound();case"square":return this.projectSquare();default:return this.projectButt()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t}))}}const Lo=function(a,t){let e=arguments.length>2&&arguments[2]!==void 0&&arguments[2];const s=[];if(a.length===0)return s;const r=a.reduce((i,n)=>(i[i.length-1].eq(n)||i.push(new _(n)),i),[new _(a[0])]);if(r.length===1)e=!0;else if(!e){const i=r[0],n=((o,h)=>{for(let l=o.length-1;l>=0;l--)if(h(o[l],l,o))return l;return-1})(r,o=>!o.eq(i));r.splice(n+1)}return r.forEach((i,n,o)=>{let h,l;n===0?(l=o[1],h=e?i:o[o.length-1]):n===o.length-1?(h=o[n-1],l=e?i:o[0]):(h=o[n-1],l=o[n+1]),e&&o.length===1?s.push(...new ii(i,i,t).project()):!e||n!==0&&n!==o.length-1?s.push(...new de(i,h,l,t).project()):s.push(...new ii(i,n===0?l:h,t).project())}),s},Pr=a=>{const t={};return Object.keys(a).forEach(e=>{t[e]={},Object.keys(a[e]).forEach(s=>{t[e][s]=m({},a[e][s])})}),t},Ro=a=>a.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;");let Te;const Ar=a=>{if(Te||Te||(Te="Intl"in js()&&"Segmenter"in Intl&&new Intl.Segmenter(void 0,{granularity:"grapheme"})),Te){const t=Te.segment(a);return Array.from(t).map(e=>{let{segment:s}=e;return s})}return Bo(a)},Bo=a=>{const t=[];for(let e,s=0;s<a.length;s++)(e=Io(a,s))!==!1&&t.push(e);return t},Io=(a,t)=>{const e=a.charCodeAt(t);if(isNaN(e))return"";if(e<55296||e>57343)return a.charAt(t);if(55296<=e&&e<=56319){if(a.length<=t+1)throw"High surrogate without following low surrogate";const r=a.charCodeAt(t+1);if(56320>r||r>57343)throw"High surrogate without following low surrogate";return a.charAt(t)+a.charAt(t+1)}if(t===0)throw"Low surrogate without preceding high surrogate";const s=a.charCodeAt(t-1);if(55296>s||s>56319)throw"Low surrogate without preceding high surrogate";return!1},Fr=function(a,t){let e=arguments.length>2&&arguments[2]!==void 0&&arguments[2];return a.fill!==t.fill||a.stroke!==t.stroke||a.strokeWidth!==t.strokeWidth||a.fontSize!==t.fontSize||a.fontFamily!==t.fontFamily||a.fontWeight!==t.fontWeight||a.fontStyle!==t.fontStyle||a.textBackgroundColor!==t.textBackgroundColor||a.deltaY!==t.deltaY||e&&(a.overline!==t.overline||a.underline!==t.underline||a.linethrough!==t.linethrough)},Xo=(a,t)=>{const e=t.split(`
`),s=[];let r=-1,i={};a=Pr(a);for(let n=0;n<e.length;n++){const o=Ar(e[n]);if(a[n])for(let h=0;h<o.length;h++){r++;const l=a[n][h];l&&Object.keys(l).length>0&&(Fr(i,l,!0)?s.push({start:r,end:r+1,style:l}):s[s.length-1].end++),i=l||{}}else r+=o.length,i={}}return s},Wo=(a,t)=>{if(!Array.isArray(a))return Pr(a);const e=t.split(Cr),s={};let r=-1,i=0;for(let n=0;n<e.length;n++){const o=Ar(e[n]);for(let h=0;h<o.length;h++)r++,a[i]&&a[i].start<=r&&r<a[i].end&&(s[n]=s[n]||{},s[n][h]=m({},a[i].style),r===a[i].end-1&&i++)}return s},qt=["display","transform",K,"fill-opacity","fill-rule","opacity",nt,"stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"];function ni(a,t){const e=a.nodeName,s=a.getAttribute("class"),r=a.getAttribute("id"),i="(?![a-zA-Z\\-]+)";let n;if(n=new RegExp("^"+e,"i"),t=t.replace(n,""),r&&t.length&&(n=new RegExp("#"+r+i,"i"),t=t.replace(n,"")),s&&t.length){const o=s.split(" ");for(let h=o.length;h--;)n=new RegExp("\\."+o[h]+i,"i"),t=t.replace(n,"")}return t.length===0}function Yo(a,t){let e=!0;const s=ni(a,t.pop());return s&&t.length&&(e=function(r,i){let n,o=!0;for(;r.parentElement&&r.parentElement.nodeType===1&&i.length;)o&&(n=i.pop()),o=ni(r=r.parentElement,n);return i.length===0}(a,t)),s&&e&&t.length===0}const Vo=a=>{var t;return(t=Qn[a])!==null&&t!==void 0?t:a},zo=new RegExp("(".concat(ie,")"),"gi"),Ho=a=>a.replace(zo," $1 ").replace(/,/gi," ").replace(/\s+/gi," ");var oi,ai,hi,li,ci,ui,gi;const Q="(".concat(ie,")"),Go=String.raw(oi||(oi=Ut(["(skewX)(",")"],["(skewX)\\(","\\)"])),Q),No=String.raw(ai||(ai=Ut(["(skewY)(",")"],["(skewY)\\(","\\)"])),Q),Uo=String.raw(hi||(hi=Ut(["(rotate)(","(?: "," ",")?)"],["(rotate)\\(","(?: "," ",")?\\)"])),Q,Q,Q),qo=String.raw(li||(li=Ut(["(scale)(","(?: ",")?)"],["(scale)\\(","(?: ",")?\\)"])),Q,Q),Ko=String.raw(ci||(ci=Ut(["(translate)(","(?: ",")?)"],["(translate)\\(","(?: ",")?\\)"])),Q,Q),Jo=String.raw(ui||(ui=Ut(["(matrix)("," "," "," "," "," ",")"],["(matrix)\\("," "," "," "," "," ","\\)"])),Q,Q,Q,Q,Q,Q),Lr="(?:".concat(Jo,"|").concat(Ko,"|").concat(Uo,"|").concat(qo,"|").concat(Go,"|").concat(No,")"),Zo="(?:".concat(Lr,"*)"),$o=String.raw(gi||(gi=Ut(["^s*(?:","?)s*$"],["^\\s*(?:","?)\\s*$"])),Zo),Qo=new RegExp($o),ta=new RegExp(Lr),ea=new RegExp(Lr,"g");function gr(a){const t=[];if(!(a=Ho(a).replace(/\s*([()])\s*/gi,"$1"))||a&&!Qo.test(a))return[...tt];for(const e of a.matchAll(ea)){const s=ta.exec(e[0]);if(!s)continue;let r=tt;const i=s.filter(f=>!!f),[,n,...o]=i,[h,l,c,u,g,d]=o.map(f=>parseFloat(f));switch(n){case"translate":r=ts(h,l);break;case br:r=_e({angle:h},{x:l,y:c});break;case As:r=Tr(h,l);break;case ve:r=Pi(h);break;case ye:r=Ai(h);break;case"matrix":r=[h,l,c,u,g,d]}t.push(r)}return wr(t)}function sa(a,t,e,s){const r=Array.isArray(t);let i,n=t;if(a!==K&&a!==nt||t!==it){if(a==="strokeUniform")return t==="non-scaling-stroke";if(a==="strokeDashArray")n=t===it?null:t.replace(/,/g," ").split(/\s+/).map(parseFloat);else if(a==="transformMatrix")n=e&&e.transformMatrix?J(e.transformMatrix,gr(t)):gr(t);else if(a==="visible")n=t!==it&&t!=="hidden",e&&e.visible===!1&&(n=!1);else if(a==="opacity")n=parseFloat(t),e&&e.opacity!==void 0&&(n*=e.opacity);else if(a==="textAnchor")n=t==="start"?j:t==="end"?z:k;else if(a==="charSpacing")i=ge(t,s)/s*1e3;else if(a==="paintFirst"){const o=t.indexOf(K),h=t.indexOf(nt);n=K,(o>-1&&h>-1&&h<o||o===-1&&h>-1)&&(n=nt)}else{if(a==="href"||a==="xlink:href"||a==="font"||a==="id")return t;if(a==="imageSmoothing")return t==="optimizeQuality";i=r?t.map(ge):ge(t,s)}}else n="";return!r&&isNaN(i)?n:i}function ra(a,t){const e=a.match($n);if(!e)return;const s=e[1],r=e[3],i=e[4],n=e[5],o=e[6];s&&(t.fontStyle=s),r&&(t.fontWeight=isNaN(parseFloat(r))?r:parseFloat(r)),i&&(t.fontSize=ge(i)),o&&(t.fontFamily=o),n&&(t.lineHeight=n==="normal"?1:n)}function ia(a,t){a.replace(/;\s*$/,"").split(";").forEach(e=>{if(!e)return;const[s,r]=e.split(":");t[s.trim().toLowerCase()]=r.trim()})}function na(a){const t={},e=a.getAttribute("style");return e&&(typeof e=="string"?ia(e,t):function(s,r){Object.entries(s).forEach(i=>{let[n,o]=i;o!==void 0&&(r[n.toLowerCase()]=o)})}(e,t)),t}const oa={stroke:"strokeOpacity",fill:"fillOpacity"};function Wt(a,t,e){if(!a)return{};let s,r={},i=xr;a.parentNode&&Zr.test(a.parentNode.nodeName)&&(r=Wt(a.parentElement,t,e),r.fontSize&&(s=i=ge(r.fontSize)));const n=m(m(m({},t.reduce((l,c)=>{const u=a.getAttribute(c);return u&&(l[c]=u),l},{})),function(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u={};for(const g in c)Yo(l,g.split(" "))&&(u=m(m({},u),c[g]));return u}(a,e)),na(a));n[Us]&&a.setAttribute(Us,n[Us]),n[Ns]&&(s=ge(n[Ns],i),n[Ns]="".concat(s));const o={};for(const l in n){const c=Vo(l),u=sa(c,n[l],r,s);o[c]=u}o&&o.font&&ra(o.font,o);const h=m(m({},r),o);return Zr.test(a.nodeName)?h:function(l){const c=Z.getDefaults();return Object.entries(oa).forEach(u=>{let[g,d]=u;if(l[d]===void 0||l[g]==="")return;if(l[g]===void 0){if(!c[g])return;l[g]=c[g]}if(l[g].indexOf("url(")===0)return;const f=new A(l[g]);l[g]=f.setAlpha(B(f.getAlpha()*l[d],2)).toRgba()}),l}(h)}const aa=["left","top","width","height","visible"],tn=["rx","ry"];class St extends Z{static getDefaults(){return m(m({},super.getDefaults()),St.ownDefaults)}constructor(t){super(),Object.assign(this,St.ownDefaults),this.setOptions(t),this._initRxRy()}_initRxRy(){const{rx:t,ry:e}=this;t&&!e?this.ry=t:e&&!t&&(this.rx=e)}_render(t){const{width:e,height:s}=this,r=-e/2,i=-s/2,n=this.rx?Math.min(this.rx,e/2):0,o=this.ry?Math.min(this.ry,s/2):0,h=n!==0||o!==0;t.beginPath(),t.moveTo(r+n,i),t.lineTo(r+e-n,i),h&&t.bezierCurveTo(r+e-zt*n,i,r+e,i+zt*o,r+e,i+o),t.lineTo(r+e,i+s-o),h&&t.bezierCurveTo(r+e,i+s-zt*o,r+e-zt*n,i+s,r+e-n,i+s),t.lineTo(r+n,i+s),h&&t.bezierCurveTo(r+zt*n,i+s,r,i+s-zt*o,r,i+s-o),t.lineTo(r,i+o),h&&t.bezierCurveTo(r,i+zt*o,r+zt*n,i,r+n,i),t.closePath(),this._renderPaintInOrder(t)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...tn,...t])}_toSVG(){const{width:t,height:e,rx:s,ry:r}=this;return["<rect ","COMMON_PARTS",'x="'.concat(-t/2,'" y="').concat(-e/2,'" rx="').concat(s,'" ry="').concat(r,'" width="').concat(t,'" height="').concat(e,`" />
`)]}static async fromElement(t,e,s){const r=Wt(t,this.ATTRIBUTE_NAMES,s),{left:i=0,top:n=0,width:o=0,height:h=0,visible:l=!0}=r,c=I(r,aa);return new this(m(m(m({},e),c),{},{left:i,top:n,width:o,height:h,visible:!!(l&&o&&h)}))}}p(St,"type","Rect"),p(St,"cacheProperties",[...Xt,...tn]),p(St,"ownDefaults",{rx:0,ry:0}),p(St,"ATTRIBUTE_NAMES",[...qt,"x","y","rx","ry","width","height"]),T.setClass(St),T.setSVGClass(St);const jt="initialization",Ds="added",Rr="removed",ks="imperative",en=(a,t)=>{const{strokeUniform:e,strokeWidth:s,width:r,height:i,group:n}=t,o=n&&n!==a?Rs(n.calcTransformMatrix(),a.calcTransformMatrix()):null,h=o?t.getRelativeCenterPoint().transform(o):t.getRelativeCenterPoint(),l=!t.isStrokeAccountedForInDimensions(),c=e&&l?Un(new _(s,s),void 0,a.calcTransformMatrix()):Sr,u=!e&&l?s:0,g=Dr(r+u,i+u,wr([o,t.calcOwnMatrix()],!0)).add(c).scalarDivide(2);return[h.subtract(g),h.add(g)]};class Xs{calcLayoutResult(t,e){if(this.shouldPerformLayout(t))return this.calcBoundingBox(e,t)}shouldPerformLayout(t){let{type:e,prevStrategy:s,strategy:r}=t;return e===jt||e===ks||!!s&&r!==s}shouldLayoutClipPath(t){let{type:e,target:{clipPath:s}}=t;return e!==jt&&s&&!s.absolutePositioned}getInitialSize(t,e){return e.size}calcBoundingBox(t,e){const{type:s,target:r}=e;if(s===ks&&e.overrides)return e.overrides;if(t.length===0)return;const{left:i,top:n,width:o,height:h}=At(t.map(u=>en(r,u)).reduce((u,g)=>u.concat(g),[])),l=new _(o,h),c=new _(i,n).add(l.scalarDivide(2));if(s===jt){const u=this.getInitialSize(e,{size:l,center:c});return{center:c,relativeCorrection:new _(0,0),size:u}}return{center:c.transform(r.calcOwnMatrix()),size:l}}}p(Xs,"type","strategy");class dr extends Xs{shouldPerformLayout(t){return!0}}p(dr,"type","fit-content"),T.setClass(dr);const ha=["strategy"],la=["target","strategy","bubbles","prevStrategy"],sn="layoutManager";class $e{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:new dr;p(this,"strategy",void 0),this.strategy=t,this._subscriptions=new Map}performLayout(t){const e=m(m({bubbles:!0,strategy:this.strategy},t),{},{prevStrategy:this._prevLayoutStrategy,stopPropagation(){this.bubbles=!1}});this.onBeforeLayout(e);const s=this.getLayoutResult(e);s&&this.commitLayout(e,s),this.onAfterLayout(e,s),this._prevLayoutStrategy=e.strategy}attachHandlers(t,e){const{target:s}=e;return[bs,Si,Ue,wi,Ps,Ti,Cs,An,Fn].map(r=>t.on(r,i=>this.performLayout(r===bs?{type:"object_modified",trigger:r,e:i,target:s}:{type:"object_modifying",trigger:r,e:i,target:s})))}subscribe(t,e){this.unsubscribe(t,e);const s=this.attachHandlers(t,e);this._subscriptions.set(t,s)}unsubscribe(t,e){(this._subscriptions.get(t)||[]).forEach(s=>s()),this._subscriptions.delete(t)}unsubscribeTargets(t){t.targets.forEach(e=>this.unsubscribe(e,t))}subscribeTargets(t){t.targets.forEach(e=>this.subscribe(e,t))}onBeforeLayout(t){const{target:e,type:s}=t,{canvas:r}=e;if(s===jt||s===Ds?this.subscribeTargets(t):s===Rr&&this.unsubscribeTargets(t),e.fire("layout:before",{context:t}),r&&r.fire("object:layout:before",{target:e,context:t}),s===ks&&t.deep){const i=I(t,ha);e.forEachObject(n=>n.layoutManager&&n.layoutManager.performLayout(m(m({},i),{},{bubbles:!1,target:n})))}}getLayoutResult(t){const{target:e,strategy:s,type:r}=t,i=s.calcLayoutResult(t,e.getObjects());if(!i)return;const n=r===jt?new _:e.getRelativeCenterPoint(),{center:o,correction:h=new _,relativeCorrection:l=new _}=i,c=n.subtract(o).add(h).transform(r===jt?tt:xt(e.calcOwnMatrix()),!0).add(l);return{result:i,prevCenter:n,nextCenter:o,offset:c}}commitLayout(t,e){const{target:s}=t,{result:{size:r},nextCenter:i}=e;var n,o;s.set({width:r.x,height:r.y}),this.layoutObjects(t,e),t.type===jt?s.set({left:(n=t.x)!==null&&n!==void 0?n:i.x+r.x*N(s.originX),top:(o=t.y)!==null&&o!==void 0?o:i.y+r.y*N(s.originY)}):(s.setPositionByOrigin(i,k,k),s.setCoords(),s.set("dirty",!0))}layoutObjects(t,e){const{target:s}=t;s.forEachObject(r=>{r.group===s&&this.layoutObject(t,e,r)}),t.strategy.shouldLayoutClipPath(t)&&this.layoutObject(t,e,s.clipPath)}layoutObject(t,e,s){let{offset:r}=e;s.set({left:s.left+r.x,top:s.top+r.y})}onAfterLayout(t,e){const{target:s,strategy:r,bubbles:i,prevStrategy:n}=t,o=I(t,la),{canvas:h}=s;s.fire("layout:after",{context:t,result:e}),h&&h.fire("object:layout:after",{context:t,result:e,target:s});const l=s.parent;i&&l!=null&&l.layoutManager&&((o.path||(o.path=[])).push(s),l.layoutManager.performLayout(m(m({},o),{},{target:l}))),s.set("dirty",!0)}dispose(){const{_subscriptions:t}=this;t.forEach(e=>e.forEach(s=>s())),t.clear()}toObject(){return{type:sn,strategy:this.strategy.constructor.type}}toJSON(){return this.toObject()}}T.setClass($e,sn);const ca=["type","objects","layoutManager"];class ua extends $e{performLayout(){}}class re extends Oi(Z){static getDefaults(){return m(m({},super.getDefaults()),re.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),p(this,"_activeObjects",[]),p(this,"__objectSelectionTracker",void 0),p(this,"__objectSelectionDisposer",void 0),Object.assign(this,re.ownDefaults),this.setOptions(e),this.groupInit(t,e)}groupInit(t,e){var s;this._objects=[...t],this.__objectSelectionTracker=this.__objectSelectionMonitor.bind(this,!0),this.__objectSelectionDisposer=this.__objectSelectionMonitor.bind(this,!1),this.forEachObject(r=>{this.enterGroup(r,!1)}),this.layoutManager=(s=e.layoutManager)!==null&&s!==void 0?s:new $e,this.layoutManager.performLayout({type:jt,target:this,targets:[...t],x:e.left,y:e.top})}canEnterGroup(t){return t===this||this.isDescendantOf(t)?(Gt("error","Group: circular object trees are not supported, this call has no effect"),!1):this._objects.indexOf(t)===-1||(Gt("error","Group: duplicate objects are not supported inside group, this call has no effect"),!1)}_filterObjectsBeforeEnteringGroup(t){return t.filter((e,s,r)=>this.canEnterGroup(e)&&r.indexOf(e)===s)}add(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];const r=this._filterObjectsBeforeEnteringGroup(e),i=super.add(...r);return this._onAfterObjectsChange(Ds,r),i}insertAt(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),r=1;r<e;r++)s[r-1]=arguments[r];const i=this._filterObjectsBeforeEnteringGroup(s),n=super.insertAt(t,...i);return this._onAfterObjectsChange(Ds,i),n}remove(){const t=super.remove(...arguments);return this._onAfterObjectsChange(Rr,t),t}_onObjectAdded(t){this.enterGroup(t,!0),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t,e){this.exitGroup(t,e),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onAfterObjectsChange(t,e){this.layoutManager.performLayout({type:t,targets:e,target:this})}_onStackOrderChanged(){this._set("dirty",!0)}_set(t,e){const s=this[t];return super._set(t,e),t==="canvas"&&s!==e&&(this._objects||[]).forEach(r=>{r._set(t,e)}),this}_shouldSetNestedCoords(){return this.subTargetCheck}removeAll(){return this._activeObjects=[],this.remove(...this._objects)}__objectSelectionMonitor(t,e){let{target:s}=e;const r=this._activeObjects;if(t)r.push(s),this._set("dirty",!0);else if(r.length>0){const i=r.indexOf(s);i>-1&&(r.splice(i,1),this._set("dirty",!0))}}_watchObject(t,e){t&&this._watchObject(!1,e),t?(e.on("selected",this.__objectSelectionTracker),e.on("deselected",this.__objectSelectionDisposer)):(e.off("selected",this.__objectSelectionTracker),e.off("deselected",this.__objectSelectionDisposer))}enterGroup(t,e){t.group&&t.group.remove(t),t._set("parent",this),this._enterGroup(t,e)}_enterGroup(t,e){e&&Ts(t,J(xt(this.calcTransformMatrix()),t.calcTransformMatrix())),this._shouldSetNestedCoords()&&t.setCoords(),t._set("group",this),t._set("canvas",this.canvas),this._watchObject(!0,t);const s=this.canvas&&this.canvas.getActiveObject&&this.canvas.getActiveObject();s&&(s===t||t.isDescendantOf(s))&&this._activeObjects.push(t)}exitGroup(t,e){this._exitGroup(t,e),t._set("parent",void 0),t._set("canvas",void 0)}_exitGroup(t,e){t._set("group",void 0),e||(Ts(t,J(this.calcTransformMatrix(),t.calcTransformMatrix())),t.setCoords()),this._watchObject(!1,t);const s=this._activeObjects.length>0?this._activeObjects.indexOf(t):-1;s>-1&&this._activeObjects.splice(s,1)}shouldCache(){const t=Z.prototype.shouldCache.call(this);if(t){for(let e=0;e<this._objects.length;e++)if(this._objects[e].willDrawShadow())return this.ownCaching=!1,!1}return t}willDrawShadow(){if(super.willDrawShadow())return!0;for(let t=0;t<this._objects.length;t++)if(this._objects[t].willDrawShadow())return!0;return!1}isOnACache(){return this.ownCaching||!!this.parent&&this.parent.isOnACache()}drawObject(t,e,s){this._renderBackground(t);for(let i=0;i<this._objects.length;i++){var r;const n=this._objects[i];(r=this.canvas)!==null&&r!==void 0&&r.preserveObjectStacking&&n.group!==this?(t.save(),t.transform(...xt(this.calcTransformMatrix())),n.render(t),t.restore()):n.group===this&&n.render(t)}this._drawClipPath(t,this.clipPath,s)}setCoords(){super.setCoords(),this._shouldSetNestedCoords()&&this.forEachObject(t=>t.setCoords())}triggerLayout(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.layoutManager.performLayout(m({target:this,type:ks},t))}render(t){this._transformDone=!0,super.render(t),this._transformDone=!1}__serializeObjects(t,e){const s=this.includeDefaultValues;return this._objects.filter(function(r){return!r.excludeFromExport}).map(function(r){const i=r.includeDefaultValues;r.includeDefaultValues=s;const n=r[t||"toObject"](e);return r.includeDefaultValues=i,n})}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=this.layoutManager.toObject();return m(m(m({},super.toObject(["subTargetCheck","interactive",...t])),e.strategy!=="fit-content"||this.includeDefaultValues?{layoutManager:e}:{}),{},{objects:this.__serializeObjects("toObject",t)})}toString(){return"#<Group: (".concat(this.complexity(),")>")}dispose(){this.layoutManager.unsubscribeTargets({targets:this.getObjects(),target:this}),this._activeObjects=[],this.forEachObject(t=>{this._watchObject(!1,t),t.dispose()}),super.dispose()}_createSVGBgRect(t){if(!this.backgroundColor)return"";const e=St.prototype._toSVG.call(this),s=e.indexOf("COMMON_PARTS");e[s]='for="group" ';const r=e.join("");return t?t(r):r}_toSVG(t){const e=["<g ","COMMON_PARTS",` >
`],s=this._createSVGBgRect(t);s&&e.push("		",s);for(let r=0;r<this._objects.length;r++)e.push("		",this._objects[r].toSVG(t));return e.push(`</g>
`),e}getSvgStyles(){const t=this.opacity!==void 0&&this.opacity!==1?"opacity: ".concat(this.opacity,";"):"",e=this.visible?"":" visibility: hidden;";return[t,this.getSvgFilter(),e].join("")}toClipPathSVG(t){const e=[],s=this._createSVGBgRect(t);s&&e.push("	",s);for(let r=0;r<this._objects.length;r++)e.push("	",this._objects[r].toClipPathSVG(t));return this._createBaseClipPathSVGMarkup(e,{reviver:t})}static fromObject(t,e){let{type:s,objects:r=[],layoutManager:i}=t,n=I(t,ca);return Promise.all([qe(r,e),Ls(n,e)]).then(o=>{let[h,l]=o;const c=new this(h,m(m(m({},n),l),{},{layoutManager:new ua}));if(i){const u=T.getClass(i.type),g=T.getClass(i.strategy);c.layoutManager=new u(new g)}else c.layoutManager=new $e;return c.layoutManager.subscribeTargets({type:jt,target:c,targets:c.getObjects()}),c.setCoords(),c})}}p(re,"type","Group"),p(re,"ownDefaults",{strokeWidth:0,subTargetCheck:!1,interactive:!1}),T.setClass(re);const ga=(a,t)=>Math.min(t.width/a.width,t.height/a.height),da=(a,t)=>Math.max(t.width/a.width,t.height/a.height),fr="\\s*,?\\s*",Oe="".concat(fr,"(").concat(ie,")"),fa="".concat(Oe).concat(Oe).concat(Oe).concat(fr,"([01])").concat(fr,"([01])").concat(Oe).concat(Oe),pa={m:"l",M:"L"},ma=(a,t,e,s,r,i,n,o,h,l,c)=>{const u=Lt(a),g=Rt(a),d=Lt(t),f=Rt(t),v=e*r*d-s*i*f+n,y=s*r*d+e*i*f+o;return["C",l+h*(-e*r*g-s*i*u),c+h*(-s*r*g+e*i*u),v+h*(e*r*f+s*i*d),y+h*(s*r*f-e*i*d),v,y]},di=(a,t,e,s)=>{const r=Math.atan2(t,a),i=Math.atan2(s,e);return i>=r?i-r:2*Math.PI-(r-i)};function fi(a,t,e,s,r,i,n,o){let h;if(E.cachesBoundsOfCurve&&(h=[...arguments].join(),ze.boundsOfCurveCache[h]))return ze.boundsOfCurveCache[h];const l=Math.sqrt,c=Math.abs,u=[],g=[[0,0],[0,0]];let d=6*a-12*e+6*r,f=-3*a+9*e-9*r+3*n,v=3*e-3*a;for(let S=0;S<2;++S){if(S>0&&(d=6*t-12*s+6*i,f=-3*t+9*s-9*i+3*o,v=3*s-3*t),c(f)<1e-12){if(c(d)<1e-12)continue;const F=-v/d;0<F&&F<1&&u.push(F);continue}const b=d*d-4*v*f;if(b<0)continue;const D=l(b),M=(-d+D)/(2*f);0<M&&M<1&&u.push(M);const O=(-d-D)/(2*f);0<O&&O<1&&u.push(O)}let y=u.length;const x=y,C=rn(a,t,e,s,r,i,n,o);for(;y--;){const{x:S,y:b}=C(u[y]);g[0][y]=S,g[1][y]=b}g[0][x]=a,g[1][x]=t,g[0][x+1]=n,g[1][x+1]=o;const w=[new _(Math.min(...g[0]),Math.min(...g[1])),new _(Math.max(...g[0]),Math.max(...g[1]))];return E.cachesBoundsOfCurve&&(ze.boundsOfCurveCache[h]=w),w}const va=(a,t,e)=>{let[s,r,i,n,o,h,l,c]=e;const u=((g,d,f,v,y,x,C)=>{if(f===0||v===0)return[];let w=0,S=0,b=0;const D=Math.PI,M=C*_r,O=Rt(M),F=Lt(M),L=.5*(-F*g-O*d),q=.5*(-F*d+O*g),V=f**2,P=v**2,W=q**2,at=L**2,et=V*P-V*W-P*at;let ft=Math.abs(f),Y=Math.abs(v);if(et<0){const Vt=Math.sqrt(1-et/(V*P));ft*=Vt,Y*=Vt}else b=(y===x?-1:1)*Math.sqrt(et/(V*W+P*at));const bt=b*ft*q/Y,Dt=-b*Y*L/ft,ss=F*bt-O*Dt+.5*g,Vs=O*bt+F*Dt+.5*d;let Se=di(1,0,(L-bt)/ft,(q-Dt)/Y),Yt=di((L-bt)/ft,(q-Dt)/Y,(-L-bt)/ft,(-q-Dt)/Y);x===0&&Yt>0?Yt-=2*D:x===1&&Yt<0&&(Yt+=2*D);const rs=Math.ceil(Math.abs(Yt/D*2)),Kt=[],we=Yt/rs,Dn=8/3*Math.sin(we/4)*Math.sin(we/4)/Math.sin(we/2);let zs=Se+we;for(let Vt=0;Vt<rs;Vt++)Kt[Vt]=ma(Se,zs,F,O,ft,Y,ss,Vs,Dn,w,S),w=Kt[Vt][5],S=Kt[Vt][6],Se=zs,zs+=we;return Kt})(l-a,c-t,r,i,o,h,n);for(let g=0,d=u.length;g<d;g++)u[g][1]+=a,u[g][2]+=t,u[g][3]+=a,u[g][4]+=t,u[g][5]+=a,u[g][6]+=t;return u},ya=a=>{let t=0,e=0,s=0,r=0;const i=[];let n,o=0,h=0;for(const l of a){const c=[...l];let u;switch(c[0]){case"l":c[1]+=t,c[2]+=e;case"L":t=c[1],e=c[2],u=["L",t,e];break;case"h":c[1]+=t;case"H":t=c[1],u=["L",t,e];break;case"v":c[1]+=e;case"V":e=c[1],u=["L",t,e];break;case"m":c[1]+=t,c[2]+=e;case"M":t=c[1],e=c[2],s=c[1],r=c[2],u=["M",t,e];break;case"c":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e,c[5]+=t,c[6]+=e;case"C":o=c[3],h=c[4],t=c[5],e=c[6],u=["C",c[1],c[2],o,h,t,e];break;case"s":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e;case"S":n==="C"?(o=2*t-o,h=2*e-h):(o=t,h=e),t=c[3],e=c[4],u=["C",o,h,c[1],c[2],t,e],o=u[3],h=u[4];break;case"q":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e;case"Q":o=c[1],h=c[2],t=c[3],e=c[4],u=["Q",o,h,t,e];break;case"t":c[1]+=t,c[2]+=e;case"T":n==="Q"?(o=2*t-o,h=2*e-h):(o=t,h=e),t=c[1],e=c[2],u=["Q",o,h,t,e];break;case"a":c[6]+=t,c[7]+=e;case"A":va(t,e,c).forEach(g=>i.push(g)),t=c[6],e=c[7];break;case"z":case"Z":t=s,e=r,u=["Z"]}u?(i.push(u),n=u[0]):n=""}return i},Ms=(a,t,e,s)=>Math.sqrt((e-a)**2+(s-t)**2),rn=(a,t,e,s,r,i,n,o)=>h=>{const l=h**3,c=(d=>3*d**2*(1-d))(h),u=(d=>3*d*(1-d)**2)(h),g=(d=>(1-d)**3)(h);return new _(n*l+r*c+e*u+a*g,o*l+i*c+s*u+t*g)},nn=a=>a**2,on=a=>2*a*(1-a),an=a=>(1-a)**2,_a=(a,t,e,s,r,i,n,o)=>h=>{const l=nn(h),c=on(h),u=an(h),g=3*(u*(e-a)+c*(r-e)+l*(n-r)),d=3*(u*(s-t)+c*(i-s)+l*(o-i));return Math.atan2(d,g)},xa=(a,t,e,s,r,i)=>n=>{const o=nn(n),h=on(n),l=an(n);return new _(r*o+e*h+a*l,i*o+s*h+t*l)},Ca=(a,t,e,s,r,i)=>n=>{const o=1-n,h=2*(o*(e-a)+n*(r-e)),l=2*(o*(s-t)+n*(i-s));return Math.atan2(l,h)},pi=(a,t,e)=>{let s=new _(t,e),r=0;for(let i=1;i<=100;i+=1){const n=a(i/100);r+=Ms(s.x,s.y,n.x,n.y),s=n}return r},ba=(a,t)=>{let e,s=0,r=0,i={x:a.x,y:a.y},n=m({},i),o=.01,h=0;const l=a.iterator,c=a.angleFinder;for(;r<t&&o>1e-4;)n=l(s),h=s,e=Ms(i.x,i.y,n.x,n.y),e+r>t?(s-=o,o/=2):(i=n,s+=o,r+=e);return m(m({},n),{},{angle:c(h)})},hn=a=>{let t,e,s=0,r=0,i=0,n=0,o=0;const h=[];for(const l of a){const c={x:r,y:i,command:l[0],length:0};switch(l[0]){case"M":e=c,e.x=n=r=l[1],e.y=o=i=l[2];break;case"L":e=c,e.length=Ms(r,i,l[1],l[2]),r=l[1],i=l[2];break;case"C":t=rn(r,i,l[1],l[2],l[3],l[4],l[5],l[6]),e=c,e.iterator=t,e.angleFinder=_a(r,i,l[1],l[2],l[3],l[4],l[5],l[6]),e.length=pi(t,r,i),r=l[5],i=l[6];break;case"Q":t=xa(r,i,l[1],l[2],l[3],l[4]),e=c,e.iterator=t,e.angleFinder=Ca(r,i,l[1],l[2],l[3],l[4]),e.length=pi(t,r,i),r=l[3],i=l[4];break;case"Z":e=c,e.destX=n,e.destY=o,e.length=Ms(r,i,n,o),r=n,i=o}s+=e.length,h.push(e)}return h.push({length:s,x:r,y:i}),h},Sa=function(a,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:hn(a),s=0;for(;t-e[s].length>0&&s<e.length-2;)t-=e[s].length,s++;const r=e[s],i=t/r.length,n=a[s];switch(r.command){case"M":return{x:r.x,y:r.y,angle:0};case"Z":return m(m({},new _(r.x,r.y).lerp(new _(r.destX,r.destY),i)),{},{angle:Math.atan2(r.destY-r.y,r.destX-r.x)});case"L":return m(m({},new _(r.x,r.y).lerp(new _(n[1],n[2]),i)),{},{angle:Math.atan2(n[2]-r.y,n[1]-r.x)});case"C":case"Q":return ba(r,t)}},wa=new RegExp("[mzlhvcsqta][^mzlhvcsqta]*","gi"),mi=new RegExp(fa,"g"),Ta=new RegExp(ie,"gi"),Oa={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},Da=a=>{var t;const e=[],s=(t=a.match(wa))!==null&&t!==void 0?t:[];for(const r of s){const i=r[0];if(i==="z"||i==="Z"){e.push([i]);continue}const n=Oa[i.toLowerCase()];let o=[];if(i==="a"||i==="A"){mi.lastIndex=0;for(let h=null;h=mi.exec(r);)o.push(...h.slice(1))}else o=r.match(Ta)||[];for(let h=0;h<o.length;h+=n){const l=new Array(n),c=pa[i];l[0]=h>0&&c?c:i;for(let u=0;u<n;u++)l[u+1]=parseFloat(o[h+u]);e.push(l)}}return e},ka=(a,t)=>a.map(e=>e.map((s,r)=>r===0||t===void 0?s:B(s,t)).join(" ")).join(" ");function pr(a,t){const e=a.style;e&&t&&(typeof t=="string"?e.cssText+=";"+t:Object.entries(t).forEach(s=>{let[r,i]=s;return e.setProperty(r,i)}))}class Ma extends Bi{constructor(t){let{allowTouchScrolling:e=!1,containerClass:s=""}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(t),p(this,"upper",void 0),p(this,"container",void 0);const{el:r}=this.lower,i=this.createUpperCanvas();this.upper={el:i,ctx:i.getContext("2d")},this.applyCanvasStyle(r,{allowTouchScrolling:e}),this.applyCanvasStyle(i,{allowTouchScrolling:e,styles:{position:"absolute",left:"0",top:"0"}});const n=this.createContainerElement();n.classList.add(s),r.parentNode&&r.parentNode.replaceChild(n,r),n.append(r,i),this.container=n}createUpperCanvas(){const{el:t}=this.lower,e=Bt();return e.className=t.className,e.classList.remove("lower-canvas"),e.classList.add("upper-canvas"),e.setAttribute("data-fabric","top"),e.style.cssText=t.style.cssText,e.setAttribute("draggable","true"),e}createContainerElement(){const t=me().createElement("div");return t.setAttribute("data-fabric","wrapper"),pr(t,{position:"relative"}),zr(t),t}applyCanvasStyle(t,e){const{styles:s,allowTouchScrolling:r}=e;pr(t,m(m({},s),{},{"touch-action":r?"manipulation":it})),zr(t)}setDimensions(t,e){super.setDimensions(t,e);const{el:s,ctx:r}=this.upper;Ri(s,r,t,e)}setCSSDimensions(t){super.setCSSDimensions(t),ar(this.upper.el,t),ar(this.container,t)}cleanupDOM(t){const e=this.container,{el:s}=this.lower,{el:r}=this.upper;super.cleanupDOM(t),e.removeChild(r),e.removeChild(s),e.parentNode&&e.parentNode.replaceChild(s,e)}dispose(){super.dispose(),Ot().dispose(this.upper.el),delete this.upper,delete this.container}}class Ws extends es{constructor(){super(...arguments),p(this,"targets",[]),p(this,"_hoveredTargets",[]),p(this,"_objectsToRender",void 0),p(this,"_currentTransform",null),p(this,"_groupSelector",null),p(this,"contextTopDirty",!1)}static getDefaults(){return m(m({},super.getDefaults()),Ws.ownDefaults)}get upperCanvasEl(){var t;return(t=this.elements.upper)===null||t===void 0?void 0:t.el}get contextTop(){var t;return(t=this.elements.upper)===null||t===void 0?void 0:t.ctx}get wrapperEl(){return this.elements.container}initElements(t){this.elements=new Ma(t,{allowTouchScrolling:this.allowTouchScrolling,containerClass:this.containerClass}),this._createCacheCanvas()}_onObjectAdded(t){this._objectsToRender=void 0,super._onObjectAdded(t)}_onObjectRemoved(t){this._objectsToRender=void 0,t===this._activeObject&&(this.fire("before:selection:cleared",{deselected:[t]}),this._discardActiveObject(),this.fire("selection:cleared",{deselected:[t]}),t.fire("deselected",{target:t})),t===this._hoveredTarget&&(this._hoveredTarget=void 0,this._hoveredTargets=[]),super._onObjectRemoved(t)}_onStackOrderChanged(){this._objectsToRender=void 0,super._onStackOrderChanged()}_chooseObjectsToRender(){const t=this._activeObject;return!this.preserveObjectStacking&&t?this._objects.filter(e=>!e.group&&e!==t).concat(t):this._objects}renderAll(){this.cancelRequestedRender(),this.destroyed||(!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1),!this._objectsToRender&&(this._objectsToRender=this._chooseObjectsToRender()),this.renderCanvas(this.getContext(),this._objectsToRender))}renderTopLayer(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()}renderTop(){const t=this.contextTop;this.clearContext(t),this.renderTopLayer(t),this.fire("after:render",{ctx:t})}setTargetFindTolerance(t){t=Math.round(t),this.targetFindTolerance=t;const e=this.getRetinaScaling(),s=Math.ceil((2*t+1)*e);this.pixelFindCanvasEl.width=this.pixelFindCanvasEl.height=s,this.pixelFindContext.scale(e,e)}isTargetTransparent(t,e,s){const r=this.targetFindTolerance,i=this.pixelFindContext;this.clearContext(i),i.save(),i.translate(-e+r,-s+r),i.transform(...this.viewportTransform);const n=t.selectionBackgroundColor;t.selectionBackgroundColor="",t.render(i),t.selectionBackgroundColor=n,i.restore();const o=Math.round(r*this.getRetinaScaling());return Ao(i,o,o,o)}_isSelectionKeyPressed(t){const e=this.selectionKey;return!!e&&(Array.isArray(e)?!!e.find(s=>!!s&&t[s]===!0):t[e])}_shouldClearSelection(t,e){const s=this.getActiveObjects(),r=this._activeObject;return!!(!e||e&&r&&s.length>1&&s.indexOf(e)===-1&&r!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&r&&r!==e)}_shouldCenterTransform(t,e,s){if(!t)return;let r;return e===As||e===ot||e===gt||e===Ue?r=this.centeredScaling||t.centeredScaling:e===br&&(r=this.centeredRotation||t.centeredRotation),r?!s:s}_getOriginFromCorner(t,e){const s={x:t.originX,y:t.originY};return e&&(["ml","tl","bl"].includes(e)?s.x=z:["mr","tr","br"].includes(e)&&(s.x=j),["tl","mt","tr"].includes(e)?s.y=or:["bl","mb","br"].includes(e)&&(s.y=rt)),s}_setupCurrentTransform(t,e,s){var r;const i=e.group?ue(this.getScenePoint(t),void 0,e.group.calcTransformMatrix()):this.getScenePoint(t),{key:n="",control:o}=e.getActiveControl()||{},h=s&&o?(r=o.getActionHandler(t,e,o))===null||r===void 0?void 0:r.bind(o):Jn,l=((d,f,v,y)=>{if(!f||!d)return"drag";const x=y.controls[f];return x.getActionName(v,x,y)})(s,n,t,e),c=t[this.centeredKey],u=this._shouldCenterTransform(e,l,c)?{x:k,y:k}:this._getOriginFromCorner(e,n),g={target:e,action:l,actionHandler:h,actionPerformed:!1,corner:n,scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,offsetX:i.x-e.left,offsetY:i.y-e.top,originX:u.x,originY:u.y,ex:i.x,ey:i.y,lastX:i.x,lastY:i.y,theta:H(e.angle),width:e.width,height:e.height,shiftKey:t.shiftKey,altKey:c,original:m(m({},Ii(e)),{},{originX:u.x,originY:u.y})};this._currentTransform=g,this.fire("before:transform",{e:t,transform:g})}setCursor(t){this.upperCanvasEl.style.cursor=t}_drawSelection(t){const{x:e,y:s,deltaX:r,deltaY:i}=this._groupSelector,n=new _(e,s).transform(this.viewportTransform),o=new _(e+r,s+i).transform(this.viewportTransform),h=this.selectionLineWidth/2;let l=Math.min(n.x,o.x),c=Math.min(n.y,o.y),u=Math.max(n.x,o.x),g=Math.max(n.y,o.y);this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(l,c,u-l,g-c)),this.selectionLineWidth&&this.selectionBorderColor&&(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,l+=h,c+=h,u-=h,g-=h,Z.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(l,c,u-l,g-c))}findTarget(t){if(this.skipTargetFind)return;const e=this.getViewportPoint(t),s=this._activeObject,r=this.getActiveObjects();if(this.targets=[],s&&r.length>=1){if(s.findControl(e,hr(t))||r.length>1&&this.searchPossibleTargets([s],e))return s;if(s===this.searchPossibleTargets([s],e)){if(this.preserveObjectStacking){const i=this.targets;this.targets=[];const n=this.searchPossibleTargets(this._objects,e);return t[this.altSelectionKey]&&n&&n!==s?(this.targets=i,s):n}return s}}return this.searchPossibleTargets(this._objects,e)}_pointIsInObjectSelectionArea(t,e){let s=t.getCoords();const r=this.getZoom(),i=t.padding/r;if(i){const[n,o,h,l]=s,c=Math.atan2(o.y-n.y,o.x-n.x),u=Lt(c)*i,g=Rt(c)*i,d=u+g,f=u-g;s=[new _(n.x-f,n.y-d),new _(o.x+d,o.y-f),new _(h.x+f,h.y+d),new _(l.x-d,l.y+f)]}return X.isPointInPolygon(e,s)}_checkTarget(t,e){return!!(t&&t.visible&&t.evented&&this._pointIsInObjectSelectionArea(t,ue(e,void 0,this.viewportTransform))&&(!this.perPixelTargetFind&&!t.perPixelTargetFind||t.isEditing||!this.isTargetTransparent(t,e.x,e.y)))}_searchPossibleTargets(t,e){let s=t.length;for(;s--;){const r=t[s];if(this._checkTarget(r,e)){if(ps(r)&&r.subTargetCheck){const i=this._searchPossibleTargets(r._objects,e);i&&this.targets.push(i)}return r}}}searchPossibleTargets(t,e){const s=this._searchPossibleTargets(t,e);if(s&&ps(s)&&s.interactive&&this.targets[0]){const r=this.targets;for(let i=r.length-1;i>0;i--){const n=r[i];if(!ps(n)||!n.interactive)return n}return r[0]}return s}getViewportPoint(t){return this._pointer?this._pointer:this.getPointer(t,!0)}getScenePoint(t){return this._absolutePointer?this._absolutePointer:this.getPointer(t)}getPointer(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];const s=this.upperCanvasEl,r=s.getBoundingClientRect();let i=zn(t),n=r.width||0,o=r.height||0;n&&o||(rt in r&&or in r&&(o=Math.abs(r.top-r.bottom)),z in r&&j in r&&(n=Math.abs(r.right-r.left))),this.calcOffset(),i.x=i.x-this._offset.left,i.y=i.y-this._offset.top,e||(i=ue(i,void 0,this.viewportTransform));const h=this.getRetinaScaling();h!==1&&(i.x/=h,i.y/=h);const l=n===0||o===0?new _(1,1):new _(s.width/n,s.height/o);return i.multiply(l)}_setDimensionsImpl(t,e){this._resetTransformEventData(),super._setDimensionsImpl(t,e),this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop)}_createCacheCanvas(){this.pixelFindCanvasEl=Bt(),this.pixelFindContext=this.pixelFindCanvasEl.getContext("2d",{willReadFrequently:!0}),this.setTargetFindTolerance(this.targetFindTolerance)}getTopContext(){return this.elements.upper.ctx}getSelectionContext(){return this.elements.upper.ctx}getSelectionElement(){return this.elements.upper.el}getActiveObject(){return this._activeObject}getActiveObjects(){const t=this._activeObject;return Jt(t)?t.getObjects():t?[t]:[]}_fireSelectionEvents(t,e){let s=!1,r=!1;const i=this.getActiveObjects(),n=[],o=[];t.forEach(h=>{i.includes(h)||(s=!0,h.fire("deselected",{e,target:h}),o.push(h))}),i.forEach(h=>{t.includes(h)||(s=!0,h.fire("selected",{e,target:h}),n.push(h))}),t.length>0&&i.length>0?(r=!0,s&&this.fire("selection:updated",{e,selected:n,deselected:o})):i.length>0?(r=!0,this.fire("selection:created",{e,selected:n})):t.length>0&&(r=!0,this.fire("selection:cleared",{e,deselected:o})),r&&(this._objectsToRender=void 0)}setActiveObject(t,e){const s=this.getActiveObjects(),r=this._setActiveObject(t,e);return this._fireSelectionEvents(s,e),r}_setActiveObject(t,e){const s=this._activeObject;return s!==t&&!(!this._discardActiveObject(e,t)&&this._activeObject)&&!t.onSelect({e})&&(this._activeObject=t,Jt(t)&&s!==t&&t.set("canvas",this),t.setCoords(),!0)}_discardActiveObject(t,e){const s=this._activeObject;return!!s&&!s.onDeselect({e:t,object:e})&&(this._currentTransform&&this._currentTransform.target===s&&this.endCurrentTransform(t),Jt(s)&&s===this._hoveredTarget&&(this._hoveredTarget=void 0),this._activeObject=void 0,!0)}discardActiveObject(t){const e=this.getActiveObjects(),s=this.getActiveObject();e.length&&this.fire("before:selection:cleared",{e:t,deselected:[s]});const r=this._discardActiveObject(t);return this._fireSelectionEvents(e,t),r}endCurrentTransform(t){const e=this._currentTransform;this._finalizeCurrentTransform(t),e&&e.target&&(e.target.isMoving=!1),this._currentTransform=null}_finalizeCurrentTransform(t){const e=this._currentTransform,s=e.target,r={e:t,target:s,transform:e,action:e.action};s._scaling&&(s._scaling=!1),s.setCoords(),e.actionPerformed&&(this.fire("object:modified",r),s.fire(bs,r))}setViewportTransform(t){super.setViewportTransform(t);const e=this._activeObject;e&&e.setCoords()}destroy(){const t=this._activeObject;Jt(t)&&(t.removeAll(),t.dispose()),delete this._activeObject,super.destroy(),this.pixelFindContext=null,this.pixelFindCanvasEl=void 0}clear(){this.discardActiveObject(),this._activeObject=void 0,this.clearContext(this.contextTop),super.clear()}drawControls(t){const e=this._activeObject;e&&e._renderControls(t)}_toObject(t,e,s){const r=this._realizeGroupTransformOnObject(t),i=super._toObject(t,e,s);return t.set(r),i}_realizeGroupTransformOnObject(t){const{group:e}=t;if(e&&Jt(e)&&this._activeObject===e){const s=xe(t,["angle","flipX","flipY",j,ot,gt,ve,ye,rt]);return Gn(t,e.calcOwnMatrix()),s}return{}}_setSVGObject(t,e,s){const r=this._realizeGroupTransformOnObject(e);super._setSVGObject(t,e,s),e.set(r)}}p(Ws,"ownDefaults",{uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",selection:!0,selectionKey:"shiftKey",selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,enablePointerEvents:!1,containerClass:"canvas-container",preserveObjectStacking:!1});class Ea{constructor(t){p(this,"targets",[]),p(this,"__disposer",void 0);const e=()=>{const{hiddenTextarea:r}=t.getActiveObject()||{};r&&r.focus()},s=t.upperCanvasEl;s.addEventListener("click",e),this.__disposer=()=>s.removeEventListener("click",e)}exitTextEditing(){this.target=void 0,this.targets.forEach(t=>{t.isEditing&&t.exitEditing()})}add(t){this.targets.push(t)}remove(t){this.unregister(t),he(this.targets,t)}register(t){this.target=t}unregister(t){t===this.target&&(this.target=void 0)}onMouseMove(t){var e;!((e=this.target)===null||e===void 0)&&e.isEditing&&this.target.updateSelectionOnMouseMove(t)}clear(){this.targets=[],this.target=void 0}dispose(){this.clear(),this.__disposer(),delete this.__disposer}}const ja=["target","oldTarget","fireCanvas","e"],ht={passive:!1},ae=(a,t)=>{const e=a.getViewportPoint(t),s=a.getScenePoint(t);return{viewportPoint:e,scenePoint:s,pointer:e,absolutePointer:s}},Ht=function(a){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];return a.addEventListener(...e)},ct=function(a){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];return a.removeEventListener(...e)},Pa={mouse:{in:"over",out:"out",targetIn:"mouseover",targetOut:"mouseout",canvasIn:"mouse:over",canvasOut:"mouse:out"},drag:{in:"enter",out:"leave",targetIn:"dragenter",targetOut:"dragleave",canvasIn:"drag:enter",canvasOut:"drag:leave"}};class mr extends Ws{constructor(t){super(t,arguments.length>1&&arguments[1]!==void 0?arguments[1]:{}),p(this,"_isClick",void 0),p(this,"textEditingManager",new Ea(this)),["_onMouseDown","_onTouchStart","_onMouseMove","_onMouseUp","_onTouchEnd","_onResize","_onMouseWheel","_onMouseOut","_onMouseEnter","_onContextMenu","_onClick","_onDragStart","_onDragEnd","_onDragProgress","_onDragOver","_onDragEnter","_onDragLeave","_onDrop"].forEach(e=>{this[e]=this[e].bind(this)}),this.addOrRemove(Ht,"add")}_getEventPrefix(){return this.enablePointerEvents?"pointer":"mouse"}addOrRemove(t,e){const s=this.upperCanvasEl,r=this._getEventPrefix();t(Li(s),"resize",this._onResize),t(s,r+"down",this._onMouseDown),t(s,"".concat(r,"move"),this._onMouseMove,ht),t(s,"".concat(r,"out"),this._onMouseOut),t(s,"".concat(r,"enter"),this._onMouseEnter),t(s,"wheel",this._onMouseWheel),t(s,"contextmenu",this._onContextMenu),t(s,"click",this._onClick),t(s,"dblclick",this._onClick),t(s,"dragstart",this._onDragStart),t(s,"dragend",this._onDragEnd),t(s,"dragover",this._onDragOver),t(s,"dragenter",this._onDragEnter),t(s,"dragleave",this._onDragLeave),t(s,"drop",this._onDrop),this.enablePointerEvents||t(s,"touchstart",this._onTouchStart,ht)}removeListeners(){this.addOrRemove(ct,"remove");const t=this._getEventPrefix(),e=_t(this.upperCanvasEl);ct(e,"".concat(t,"up"),this._onMouseUp),ct(e,"touchend",this._onTouchEnd,ht),ct(e,"".concat(t,"move"),this._onMouseMove,ht),ct(e,"touchmove",this._onMouseMove,ht),clearTimeout(this._willAddMouseDown)}_onMouseWheel(t){this.__onMouseWheel(t)}_onMouseOut(t){const e=this._hoveredTarget,s=m({e:t},ae(this,t));this.fire("mouse:out",m(m({},s),{},{target:e})),this._hoveredTarget=void 0,e&&e.fire("mouseout",m({},s)),this._hoveredTargets.forEach(r=>{this.fire("mouse:out",m(m({},s),{},{target:r})),r&&r.fire("mouseout",m({},s))}),this._hoveredTargets=[]}_onMouseEnter(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",m({e:t},ae(this,t))),this._hoveredTarget=void 0,this._hoveredTargets=[])}_onDragStart(t){this._isClick=!1;const e=this.getActiveObject();if(e&&e.onDragStart(t)){this._dragSource=e;const s={e:t,target:e};return this.fire("dragstart",s),e.fire("dragstart",s),void Ht(this.upperCanvasEl,"drag",this._onDragProgress)}Hr(t)}_renderDragEffects(t,e,s){let r=!1;const i=this._dropTarget;i&&i!==e&&i!==s&&(i.clearContextTop(),r=!0),e==null||e.clearContextTop(),s!==e&&(s==null||s.clearContextTop());const n=this.contextTop;n.save(),n.transform(...this.viewportTransform),e&&(n.save(),e.transform(n),e.renderDragSourceEffect(t),n.restore(),r=!0),s&&(n.save(),s.transform(n),s.renderDropTargetEffect(t),n.restore(),r=!0),n.restore(),r&&(this.contextTopDirty=!0)}_onDragEnd(t){const e=!!t.dataTransfer&&t.dataTransfer.dropEffect!==it,s=e?this._activeObject:void 0,r={e:t,target:this._dragSource,subTargets:this.targets,dragSource:this._dragSource,didDrop:e,dropTarget:s};ct(this.upperCanvasEl,"drag",this._onDragProgress),this.fire("dragend",r),this._dragSource&&this._dragSource.fire("dragend",r),delete this._dragSource,this._onMouseUp(t)}_onDragProgress(t){const e={e:t,target:this._dragSource,dragSource:this._dragSource,dropTarget:this._draggedoverTarget};this.fire("drag",e),this._dragSource&&this._dragSource.fire("drag",e)}findDragTargets(t){return this.targets=[],{target:this._searchPossibleTargets(this._objects,this.getViewportPoint(t)),targets:[...this.targets]}}_onDragOver(t){const e="dragover",{target:s,targets:r}=this.findDragTargets(t),i=this._dragSource,n={e:t,target:s,subTargets:r,dragSource:i,canDrop:!1,dropTarget:void 0};let o;this.fire(e,n),this._fireEnterLeaveEvents(s,n),s&&(s.canDrop(t)&&(o=s),s.fire(e,n));for(let h=0;h<r.length;h++){const l=r[h];l.canDrop(t)&&(o=l),l.fire(e,n)}this._renderDragEffects(t,i,o),this._dropTarget=o}_onDragEnter(t){const{target:e,targets:s}=this.findDragTargets(t),r={e:t,target:e,subTargets:s,dragSource:this._dragSource};this.fire("dragenter",r),this._fireEnterLeaveEvents(e,r)}_onDragLeave(t){const e={e:t,target:this._draggedoverTarget,subTargets:this.targets,dragSource:this._dragSource};this.fire("dragleave",e),this._fireEnterLeaveEvents(void 0,e),this._renderDragEffects(t,this._dragSource),this._dropTarget=void 0,this.targets=[],this._hoveredTargets=[]}_onDrop(t){const{target:e,targets:s}=this.findDragTargets(t),r=this._basicEventHandler("drop:before",m({e:t,target:e,subTargets:s,dragSource:this._dragSource},ae(this,t)));r.didDrop=!1,r.dropTarget=void 0,this._basicEventHandler("drop",r),this.fire("drop:after",r)}_onContextMenu(t){const e=this.findTarget(t),s=this.targets||[],r=this._basicEventHandler("contextmenu:before",{e:t,target:e,subTargets:s});return this.stopContextMenu&&Hr(t),this._basicEventHandler("contextmenu",r),!1}_onClick(t){const e=t.detail;e>3||e<2||(this._cacheTransformEventData(t),e==2&&t.type==="dblclick"&&this._handleEvent(t,"dblclick"),e==3&&this._handleEvent(t,"tripleclick"),this._resetTransformEventData())}getPointerId(t){const e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1}_isMainEvent(t){return t.isPrimary===!0||t.isPrimary!==!1&&(t.type==="touchend"&&t.touches.length===0||!t.changedTouches||t.changedTouches[0].identifier===this.mainTouchId)}_onTouchStart(t){let e=!this.allowTouchScrolling;const s=this._activeObject;this.mainTouchId===void 0&&(this.mainTouchId=this.getPointerId(t)),this.__onMouseDown(t),(this.isDrawingMode||s&&this._target===s)&&(e=!0),e&&t.preventDefault(),this._resetTransformEventData();const r=this.upperCanvasEl,i=this._getEventPrefix(),n=_t(r);Ht(n,"touchend",this._onTouchEnd,ht),e&&Ht(n,"touchmove",this._onMouseMove,ht),ct(r,"".concat(i,"down"),this._onMouseDown)}_onMouseDown(t){this.__onMouseDown(t),this._resetTransformEventData();const e=this.upperCanvasEl,s=this._getEventPrefix();ct(e,"".concat(s,"move"),this._onMouseMove,ht);const r=_t(e);Ht(r,"".concat(s,"up"),this._onMouseUp),Ht(r,"".concat(s,"move"),this._onMouseMove,ht)}_onTouchEnd(t){if(t.touches.length>0)return;this.__onMouseUp(t),this._resetTransformEventData(),delete this.mainTouchId;const e=this._getEventPrefix(),s=_t(this.upperCanvasEl);ct(s,"touchend",this._onTouchEnd,ht),ct(s,"touchmove",this._onMouseMove,ht),this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout(()=>{Ht(this.upperCanvasEl,"".concat(e,"down"),this._onMouseDown),this._willAddMouseDown=0},400)}_onMouseUp(t){this.__onMouseUp(t),this._resetTransformEventData();const e=this.upperCanvasEl,s=this._getEventPrefix();if(this._isMainEvent(t)){const r=_t(this.upperCanvasEl);ct(r,"".concat(s,"up"),this._onMouseUp),ct(r,"".concat(s,"move"),this._onMouseMove,ht),Ht(e,"".concat(s,"move"),this._onMouseMove,ht)}}_onMouseMove(t){const e=this.getActiveObject();!this.allowTouchScrolling&&(!e||!e.shouldStartDragging(t))&&t.preventDefault&&t.preventDefault(),this.__onMouseMove(t)}_onResize(){this.calcOffset(),this._resetTransformEventData()}_shouldRender(t){const e=this.getActiveObject();return!!e!=!!t||e&&t&&e!==t}__onMouseUp(t){var e;this._cacheTransformEventData(t),this._handleEvent(t,"up:before");const s=this._currentTransform,r=this._isClick,i=this._target,{button:n}=t;if(n)return(this.fireMiddleClick&&n===1||this.fireRightClick&&n===2)&&this._handleEvent(t,"up"),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)return void this._onMouseUpInDrawingMode(t);if(!this._isMainEvent(t))return;let o,h,l=!1;if(s&&(this._finalizeCurrentTransform(t),l=s.actionPerformed),!r){const c=i===this._activeObject;this.handleSelection(t),l||(l=this._shouldRender(i)||!c&&i===this._activeObject)}if(i){const c=i.findControl(this.getViewportPoint(t),hr(t)),{key:u,control:g}=c||{};if(h=u,i.selectable&&i!==this._activeObject&&i.activeOn==="up")this.setActiveObject(i,t),l=!0;else if(g){const d=g.getMouseUpHandler(t,i,g);d&&(o=this.getScenePoint(t),d.call(g,t,s,o.x,o.y))}i.isMoving=!1}if(s&&(s.target!==i||s.corner!==h)){const c=s.target&&s.target.controls[s.corner],u=c&&c.getMouseUpHandler(t,s.target,c);o=o||this.getScenePoint(t),u&&u.call(c,t,s,o.x,o.y)}this._setCursorFromEvent(t,i),this._handleEvent(t,"up"),this._groupSelector=null,this._currentTransform=null,i&&(i.__corner=void 0),l?this.requestRenderAll():r||(e=this._activeObject)!==null&&e!==void 0&&e.isEditing||this.renderTop()}_basicEventHandler(t,e){const{target:s,subTargets:r=[]}=e;this.fire(t,e),s&&s.fire(t,e);for(let i=0;i<r.length;i++)r[i]!==s&&r[i].fire(t,e);return e}_handleEvent(t,e,s){const r=this._target,i=this.targets||[],n=m(m(m({e:t,target:r,subTargets:i},ae(this,t)),{},{transform:this._currentTransform},e==="up:before"||e==="up"?{isClick:this._isClick,currentTarget:this.findTarget(t),currentSubTargets:this.targets}:{}),e==="down:before"||e==="down"?s:{});this.fire("mouse:".concat(e),n),r&&r.fire("mouse".concat(e),n);for(let o=0;o<i.length;o++)i[o]!==r&&i[o].fire("mouse".concat(e),n)}_onMouseDownInDrawingMode(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&(this.discardActiveObject(t),this.requestRenderAll());const e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down",{alreadySelected:!1})}_onMouseMoveInDrawingMode(t){if(this._isCurrentlyDrawing){const e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")}_onMouseUpInDrawingMode(t){const e=this.getScenePoint(t);this.freeDrawingBrush?this._isCurrentlyDrawing=!!this.freeDrawingBrush.onMouseUp({e:t,pointer:e}):this._isCurrentlyDrawing=!1,this._handleEvent(t,"up")}__onMouseDown(t){this._isClick=!0,this._cacheTransformEventData(t),this._handleEvent(t,"down:before");let e=this._target,s=!!e&&e===this._activeObject;const{button:r}=t;if(r)return(this.fireMiddleClick&&r===1||this.fireRightClick&&r===2)&&this._handleEvent(t,"down",{alreadySelected:s}),void this._resetTransformEventData();if(this.isDrawingMode)return void this._onMouseDownInDrawingMode(t);if(!this._isMainEvent(t)||this._currentTransform)return;let i=this._shouldRender(e),n=!1;if(this.handleMultiSelection(t,e)?(e=this._activeObject,n=!0,i=!0):this._shouldClearSelection(t,e)&&this.discardActiveObject(t),this.selection&&(!e||!e.selectable&&!e.isEditing&&e!==this._activeObject)){const o=this.getScenePoint(t);this._groupSelector={x:o.x,y:o.y,deltaY:0,deltaX:0}}if(s=!!e&&e===this._activeObject,e){e.selectable&&e.activeOn==="down"&&this.setActiveObject(e,t);const o=e.findControl(this.getViewportPoint(t),hr(t));if(e===this._activeObject&&(o||!n)){this._setupCurrentTransform(t,e,s);const h=o?o.control:void 0,l=this.getScenePoint(t),c=h&&h.getMouseDownHandler(t,e,h);c&&c.call(h,t,this._currentTransform,l.x,l.y)}}i&&(this._objectsToRender=void 0),this._handleEvent(t,"down",{alreadySelected:s}),i&&this.requestRenderAll()}_resetTransformEventData(){this._target=this._pointer=this._absolutePointer=void 0}_cacheTransformEventData(t){this._resetTransformEventData(),this._pointer=this.getViewportPoint(t),this._absolutePointer=ue(this._pointer,void 0,this.viewportTransform),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)}__onMouseMove(t){if(this._isClick=!1,this._cacheTransformEventData(t),this._handleEvent(t,"move:before"),this.isDrawingMode)return void this._onMouseMoveInDrawingMode(t);if(!this._isMainEvent(t))return;const e=this._groupSelector;if(e){const s=this.getScenePoint(t);e.deltaX=s.x-e.x,e.deltaY=s.y-e.y,this.renderTop()}else if(this._currentTransform)this._transformObject(t);else{const s=this.findTarget(t);this._setCursorFromEvent(t,s),this._fireOverOutEvents(t,s)}this.textEditingManager.onMouseMove(t),this._handleEvent(t,"move"),this._resetTransformEventData()}_fireOverOutEvents(t,e){const s=this._hoveredTarget,r=this._hoveredTargets,i=this.targets,n=Math.max(r.length,i.length);this.fireSyntheticInOutEvents("mouse",{e:t,target:e,oldTarget:s,fireCanvas:!0});for(let o=0;o<n;o++)this.fireSyntheticInOutEvents("mouse",{e:t,target:i[o],oldTarget:r[o]});this._hoveredTarget=e,this._hoveredTargets=this.targets.concat()}_fireEnterLeaveEvents(t,e){const s=this._draggedoverTarget,r=this._hoveredTargets,i=this.targets,n=Math.max(r.length,i.length);this.fireSyntheticInOutEvents("drag",m(m({},e),{},{target:t,oldTarget:s,fireCanvas:!0}));for(let o=0;o<n;o++)this.fireSyntheticInOutEvents("drag",m(m({},e),{},{target:i[o],oldTarget:r[o]}));this._draggedoverTarget=t}fireSyntheticInOutEvents(t,e){let{target:s,oldTarget:r,fireCanvas:i,e:n}=e,o=I(e,ja);const{targetIn:h,targetOut:l,canvasIn:c,canvasOut:u}=Pa[t],g=r!==s;if(r&&g){const d=m(m({},o),{},{e:n,target:r,nextTarget:s},ae(this,n));i&&this.fire(u,d),r.fire(l,d)}if(s&&g){const d=m(m({},o),{},{e:n,target:s,previousTarget:r},ae(this,n));i&&this.fire(c,d),s.fire(h,d)}}__onMouseWheel(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()}_transformObject(t){const e=this.getScenePoint(t),s=this._currentTransform,r=s.target,i=r.group?ue(e,void 0,r.group.calcTransformMatrix()):e;s.shiftKey=t.shiftKey,s.altKey=!!this.centeredKey&&t[this.centeredKey],this._performTransformAction(t,s,i),s.actionPerformed&&this.requestRenderAll()}_performTransformAction(t,e,s){const{action:r,actionHandler:i,target:n}=e,o=!!i&&i(t,e,s.x,s.y);o&&n.setCoords(),r==="drag"&&o&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||o}_setCursorFromEvent(t,e){if(!e)return void this.setCursor(this.defaultCursor);let s=e.hoverCursor||this.hoverCursor;const r=Jt(this._activeObject)?this._activeObject:null,i=(!r||e.group!==r)&&e.findControl(this.getViewportPoint(t));if(i){const n=i.control;this.setCursor(n.cursorStyleHandler(t,n,e))}else e.subTargetCheck&&this.targets.concat().reverse().map(n=>{s=n.hoverCursor||s}),this.setCursor(s)}handleMultiSelection(t,e){const s=this._activeObject,r=Jt(s);if(s&&this._isSelectionKeyPressed(t)&&this.selection&&e&&e.selectable&&(s!==e||r)&&(r||!e.isDescendantOf(s)&&!s.isDescendantOf(e))&&!e.onSelect({e:t})&&!s.getActiveControl()){if(r){const i=s.getObjects();if(e===s){const n=this.getViewportPoint(t);if(!(e=this.searchPossibleTargets(i,n)||this.searchPossibleTargets(this._objects,n))||!e.selectable)return!1}e.group===s?(s.remove(e),this._hoveredTarget=e,this._hoveredTargets=[...this.targets],s.size()===1&&this._setActiveObject(s.item(0),t)):(s.multiSelectAdd(e),this._hoveredTarget=s,this._hoveredTargets=[...this.targets]),this._fireSelectionEvents(i,t)}else{s.isEditing&&s.exitEditing();const i=new(T.getClass("ActiveSelection"))([],{canvas:this});i.multiSelectAdd(s,e),this._hoveredTarget=i,this._setActiveObject(i,t),this._fireSelectionEvents([s],t)}return!0}return!1}handleSelection(t){if(!this.selection||!this._groupSelector)return!1;const{x:e,y:s,deltaX:r,deltaY:i}=this._groupSelector,n=new _(e,s),o=n.add(new _(r,i)),h=n.min(o),l=n.max(o).subtract(h),c=this.collectObjects({left:h.x,top:h.y,width:l.x,height:l.y},{includeIntersecting:!this.selectionFullyContained}),u=n.eq(o)?c[0]?[c[0]]:[]:c.length>1?c.filter(g=>!g.onSelect({e:t})).reverse():c;if(u.length===1)this.setActiveObject(u[0],t);else if(u.length>1){const g=T.getClass("ActiveSelection");this.setActiveObject(new g(u,{canvas:this}),t)}return this._groupSelector=null,!0}clear(){this.textEditingManager.clear(),super.clear()}destroy(){this.removeListeners(),this.textEditingManager.dispose(),super.destroy()}}const ln={x1:0,y1:0,x2:0,y2:0},Aa=m(m({},ln),{},{r1:0,r2:0}),ce=(a,t)=>isNaN(a)&&typeof t=="number"?t:a,Fa=/^(\d+\.\d+)%|(\d+)%$/;function cn(a){return a&&Fa.test(a)}function un(a,t){const e=typeof a=="number"?a:typeof a=="string"?parseFloat(a)/(cn(a)?100:1):NaN;return fe(0,ce(e,t),1)}const La=/\s*;\s*/,Ra=/\s*:\s*/;function Ba(a,t){let e,s;const r=a.getAttribute("style");if(r){const n=r.split(La);n[n.length-1]===""&&n.pop();for(let o=n.length;o--;){const[h,l]=n[o].split(Ra).map(c=>c.trim());h==="stop-color"?e=l:h==="stop-opacity"&&(s=l)}}const i=new A(e||a.getAttribute("stop-color")||"rgb(0,0,0)");return{offset:un(a.getAttribute("offset"),0),color:i.toRgb(),opacity:ce(parseFloat(s||a.getAttribute("stop-opacity")||""),1)*i.getAlpha()*t}}function Ia(a,t){const e=[],s=a.getElementsByTagName("stop"),r=un(t,1);for(let i=s.length;i--;)e.push(Ba(s[i],r));return e}function gn(a){return a.nodeName==="linearGradient"||a.nodeName==="LINEARGRADIENT"?"linear":"radial"}function dn(a){return a.getAttribute("gradientUnits")==="userSpaceOnUse"?"pixels":"percentage"}function mt(a,t){return a.getAttribute(t)}function Xa(a,t){return function(e,s){let r,{width:i,height:n,gradientUnits:o}=s;return Object.keys(e).reduce((h,l)=>{const c=e[l];return c==="Infinity"?r=1:c==="-Infinity"?r=0:(r=typeof c=="string"?parseFloat(c):c,typeof c=="string"&&cn(c)&&(r*=.01,o==="pixels"&&(l!=="x1"&&l!=="x2"&&l!=="r2"||(r*=i),l!=="y1"&&l!=="y2"||(r*=n)))),h[l]=r,h},{})}(gn(a)==="linear"?function(e){return{x1:mt(e,"x1")||0,y1:mt(e,"y1")||0,x2:mt(e,"x2")||"100%",y2:mt(e,"y2")||0}}(a):function(e){return{x1:mt(e,"fx")||mt(e,"cx")||"50%",y1:mt(e,"fy")||mt(e,"cy")||"50%",r1:0,x2:mt(e,"cx")||"50%",y2:mt(e,"cy")||"50%",r2:mt(e,"r")||"50%"}}(a),m(m({},t),{},{gradientUnits:dn(a)}))}class as{constructor(t){const{type:e="linear",gradientUnits:s="pixels",coords:r={},colorStops:i=[],offsetX:n=0,offsetY:o=0,gradientTransform:h,id:l}=t||{};Object.assign(this,{type:e,gradientUnits:s,coords:m(m({},e==="radial"?Aa:ln),r),colorStops:i,offsetX:n,offsetY:o,gradientTransform:h,id:l?"".concat(l,"_").concat(Nt()):Nt()})}addColorStop(t){for(const e in t){const s=new A(t[e]);this.colorStops.push({offset:parseFloat(e),color:s.toRgb(),opacity:s.getAlpha()})}return this}toObject(t){return m(m({},xe(this,t)),{},{type:this.type,coords:m({},this.coords),colorStops:this.colorStops.map(e=>m({},e)),offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?[...this.gradientTransform]:void 0})}toSVG(t){let{additionalTransform:e}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const s=[],r=this.gradientTransform?this.gradientTransform.concat():tt.concat(),i=this.gradientUnits==="pixels"?"userSpaceOnUse":"objectBoundingBox",n=this.colorStops.map(u=>m({},u)).sort((u,g)=>u.offset-g.offset);let o=-this.offsetX,h=-this.offsetY;var l;i==="objectBoundingBox"?(o/=t.width,h/=t.height):(o+=t.width/2,h+=t.height/2),(l=t)&&typeof l._renderPathCommands=="function"&&this.gradientUnits!=="percentage"&&(o-=t.pathOffset.x,h-=t.pathOffset.y),r[4]-=o,r[5]-=h;const c=['id="SVGID_'.concat(this.id,'"'),'gradientUnits="'.concat(i,'"'),'gradientTransform="'.concat(e?e+" ":"").concat(Ke(r),'"'),""].join(" ");if(this.type==="linear"){const{x1:u,y1:g,x2:d,y2:f}=this.coords;s.push("<linearGradient ",c,' x1="',u,'" y1="',g,'" x2="',d,'" y2="',f,`">
`)}else if(this.type==="radial"){const{x1:u,y1:g,x2:d,y2:f,r1:v,r2:y}=this.coords,x=v>y;s.push("<radialGradient ",c,' cx="',x?u:d,'" cy="',x?g:f,'" r="',x?v:y,'" fx="',x?d:u,'" fy="',x?f:g,`">
`),x&&(n.reverse(),n.forEach(w=>{w.offset=1-w.offset}));const C=Math.min(v,y);if(C>0){const w=C/Math.max(v,y);n.forEach(S=>{S.offset+=w*(1-S.offset)})}}return n.forEach(u=>{let{color:g,offset:d,opacity:f}=u;s.push("<stop ",'offset="',100*d+"%",'" style="stop-color:',g,f!==void 0?";stop-opacity: "+f:";",`"/>
`)}),s.push(this.type==="linear"?"</linearGradient>":"</radialGradient>",`
`),s.join("")}toLive(t){const{x1:e,y1:s,x2:r,y2:i,r1:n,r2:o}=this.coords,h=this.type==="linear"?t.createLinearGradient(e,s,r,i):t.createRadialGradient(e,s,n,r,i,o);return this.colorStops.forEach(l=>{let{color:c,opacity:u,offset:g}=l;h.addColorStop(g,u!==void 0?new A(c).setAlpha(u).toRgba():c)}),h}static async fromObject(t){const{colorStops:e,gradientTransform:s}=t;return new this(m(m({},t),{},{colorStops:e?e.map(r=>m({},r)):void 0,gradientTransform:s?[...s]:void 0}))}static fromElement(t,e,s){const r=dn(t),i=e._findCenterFromElement();return new this(m({id:t.getAttribute("id")||void 0,type:gn(t),coords:Xa(t,{width:s.viewBoxWidth||s.width,height:s.viewBoxHeight||s.height}),colorStops:Ia(t,s.opacity),gradientUnits:r,gradientTransform:gr(t.getAttribute("gradientTransform")||"")},r==="pixels"?{offsetX:e.width/2-i.x,offsetY:e.height/2-i.y}:{offsetX:0,offsetY:0}))}}p(as,"type","Gradient"),T.setClass(as,"gradient"),T.setClass(as,"linear"),T.setClass(as,"radial");const Wa=["type","source","patternTransform"];class Js{get type(){return"pattern"}set type(t){Gt("warn","Setting type has no effect",t)}constructor(t){p(this,"repeat","repeat"),p(this,"offsetX",0),p(this,"offsetY",0),p(this,"crossOrigin",""),this.id=Nt(),Object.assign(this,t)}isImageSource(){return!!this.source&&typeof this.source.src=="string"}isCanvasSource(){return!!this.source&&!!this.source.toDataURL}sourceToString(){return this.isImageSource()?this.source.src:this.isCanvasSource()?this.source.toDataURL():""}toLive(t){return this.source&&(!this.isImageSource()||this.source.complete&&this.source.naturalWidth!==0&&this.source.naturalHeight!==0)?t.createPattern(this.source,this.repeat):null}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const{repeat:e,crossOrigin:s}=this;return m(m({},xe(this,t)),{},{type:"pattern",source:this.sourceToString(),repeat:e,crossOrigin:s,offsetX:B(this.offsetX,E.NUM_FRACTION_DIGITS),offsetY:B(this.offsetY,E.NUM_FRACTION_DIGITS),patternTransform:this.patternTransform?[...this.patternTransform]:null})}toSVG(t){let{width:e,height:s}=t;const{source:r,repeat:i,id:n}=this,o=ce(this.offsetX/e,0),h=ce(this.offsetY/s,0),l=i==="repeat-y"||i==="no-repeat"?1+Math.abs(o||0):ce(r.width/e,0),c=i==="repeat-x"||i==="no-repeat"?1+Math.abs(h||0):ce(r.height/s,0);return['<pattern id="SVGID_'.concat(n,'" x="').concat(o,'" y="').concat(h,'" width="').concat(l,'" height="').concat(c,'">'),'<image x="0" y="0" width="'.concat(r.width,'" height="').concat(r.height,'" xlink:href="').concat(this.sourceToString(),'"></image>'),"</pattern>",""].join(`
`)}static async fromObject(t,e){let{type:s,source:r,patternTransform:i}=t,n=I(t,Wa);const o=await vs(r,m(m({},e),{},{crossOrigin:n.crossOrigin}));return new this(m(m({},n),{},{patternTransform:i&&i.slice(0),source:o}))}}p(Js,"type","Pattern"),T.setClass(Js),T.setClass(Js,"pattern");const Ya=["path","left","top"],Va=["d"];class Zt extends Z{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{path:s,left:r,top:i}=e,n=I(e,Ya);super(),Object.assign(this,Zt.ownDefaults),this.setOptions(n),this._setPath(t||[],!0),typeof r=="number"&&this.set(j,r),typeof i=="number"&&this.set(rt,i)}_setPath(t,e){this.path=ya(Array.isArray(t)?t:Da(t)),this.setBoundingBox(e)}_findCenterFromElement(){const t=this._calcBoundsFromPath();return new _(t.left+t.width/2,t.top+t.height/2)}_renderPathCommands(t){const e=-this.pathOffset.x,s=-this.pathOffset.y;t.beginPath();for(const r of this.path)switch(r[0]){case"L":t.lineTo(r[1]+e,r[2]+s);break;case"M":t.moveTo(r[1]+e,r[2]+s);break;case"C":t.bezierCurveTo(r[1]+e,r[2]+s,r[3]+e,r[4]+s,r[5]+e,r[6]+s);break;case"Q":t.quadraticCurveTo(r[1]+e,r[2]+s,r[3]+e,r[4]+s);break;case"Z":t.closePath()}}_render(t){this._renderPathCommands(t),this._renderPaintInOrder(t)}toString(){return"#<Path (".concat(this.complexity(),'): { "top": ').concat(this.top,', "left": ').concat(this.left," }>")}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject(t)),{},{path:this.path.map(e=>e.slice())})}toDatalessObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=this.toObject(t);return this.sourcePath&&(delete e.path,e.sourcePath=this.sourcePath),e}_toSVG(){const t=ka(this.path,E.NUM_FRACTION_DIGITS);return["<path ","COMMON_PARTS",'d="'.concat(t,`" stroke-linecap="round" />
`)]}_getOffsetTransform(){const t=E.NUM_FRACTION_DIGITS;return" translate(".concat(B(-this.pathOffset.x,t),", ").concat(B(-this.pathOffset.y,t),")")}toClipPathSVG(t){const e=this._getOffsetTransform();return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}toSVG(t){const e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}complexity(){return this.path.length}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{width:e,height:s,pathOffset:r}=this._calcDimensions();this.set({width:e,height:s,pathOffset:r}),t&&this.setPositionByOrigin(r,k,k)}_calcBoundsFromPath(){const t=[];let e=0,s=0,r=0,i=0;for(const n of this.path)switch(n[0]){case"L":r=n[1],i=n[2],t.push({x:e,y:s},{x:r,y:i});break;case"M":r=n[1],i=n[2],e=r,s=i;break;case"C":t.push(...fi(r,i,n[1],n[2],n[3],n[4],n[5],n[6])),r=n[5],i=n[6];break;case"Q":t.push(...fi(r,i,n[1],n[2],n[1],n[2],n[3],n[4])),r=n[3],i=n[4];break;case"Z":r=e,i=s}return At(t)}_calcDimensions(){const t=this._calcBoundsFromPath();return m(m({},t),{},{pathOffset:new _(t.left+t.width/2,t.top+t.height/2)})}static fromObject(t){return this._fromObject(t,{extraParam:"path"})}static async fromElement(t,e,s){const r=Wt(t,this.ATTRIBUTE_NAMES,s),{d:i}=r;return new this(i,m(m(m({},I(r,Va)),e),{},{left:void 0,top:void 0}))}}p(Zt,"type","Path"),p(Zt,"cacheProperties",[...Xt,"path","fillRule"]),p(Zt,"ATTRIBUTE_NAMES",[...qt,"d"]),T.setClass(Zt),T.setSVGClass(Zt);const za=["left","top","radius"],fn=["radius","startAngle","endAngle","counterClockwise"];class Mt extends Z{static getDefaults(){return m(m({},super.getDefaults()),Mt.ownDefaults)}constructor(t){super(),Object.assign(this,Mt.ownDefaults),this.setOptions(t)}_set(t,e){return super._set(t,e),t==="radius"&&this.setRadius(e),this}_render(t){t.beginPath(),t.arc(0,0,this.radius,H(this.startAngle),H(this.endAngle),this.counterClockwise),this._renderPaintInOrder(t)}getRadiusX(){return this.get("radius")*this.get(ot)}getRadiusY(){return this.get("radius")*this.get(gt)}setRadius(t){this.radius=t,this.set({width:2*t,height:2*t})}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...fn,...t])}_toSVG(){const t=(this.endAngle-this.startAngle)%360;if(t===0)return["<circle ","COMMON_PARTS",'cx="0" cy="0" ','r="',"".concat(this.radius),`" />
`];{const{radius:e}=this,s=H(this.startAngle),r=H(this.endAngle),i=Lt(s)*e,n=Rt(s)*e,o=Lt(r)*e,h=Rt(r)*e,l=t>180?1:0,c=this.counterClockwise?0:1;return['<path d="M '.concat(i," ").concat(n," A ").concat(e," ").concat(e," 0 ").concat(l," ").concat(c," ").concat(o," ").concat(h,'" '),"COMMON_PARTS",` />
`]}}static async fromElement(t,e,s){const r=Wt(t,this.ATTRIBUTE_NAMES,s),{left:i=0,top:n=0,radius:o=0}=r;return new this(m(m({},I(r,za)),{},{radius:o,left:i-o,top:n-o}))}static fromObject(t){return super._fromObject(t)}}p(Mt,"type","Circle"),p(Mt,"cacheProperties",[...Xt,...fn]),p(Mt,"ownDefaults",{radius:0,startAngle:0,endAngle:360,counterClockwise:!1}),p(Mt,"ATTRIBUTE_NAMES",["cx","cy","r",...qt]),T.setClass(Mt),T.setSVGClass(Mt);const Ha=["x1","y1","x2","y2"],Ga=["x1","y1","x2","y2"],vr=["x1","x2","y1","y2"];class $t extends Z{constructor(){let[t,e,s,r]=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[0,0,0,0],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,$t.ownDefaults),this.setOptions(i),this.x1=t,this.x2=s,this.y1=e,this.y2=r,this._setWidthHeight();const{left:n,top:o}=i;typeof n=="number"&&this.set(j,n),typeof o=="number"&&this.set(rt,o)}_setWidthHeight(){const{x1:t,y1:e,x2:s,y2:r}=this;this.width=Math.abs(s-t),this.height=Math.abs(r-e);const{left:i,top:n,width:o,height:h}=At([{x:t,y:e},{x:s,y:r}]),l=new _(i+o/2,n+h/2);this.setPositionByOrigin(l,k,k)}_set(t,e){return super._set(t,e),vr.includes(t)&&this._setWidthHeight(),this}_render(t){t.beginPath();const e=this.calcLinePoints();t.moveTo(e.x1,e.y1),t.lineTo(e.x2,e.y2),t.lineWidth=this.strokeWidth;const s=t.strokeStyle;var r;ut(this.stroke)?t.strokeStyle=this.stroke.toLive(t):t.strokeStyle=(r=this.stroke)!==null&&r!==void 0?r:t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=s}_findCenterFromElement(){return new _((this.x1+this.x2)/2,(this.y1+this.y2)/2)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject(t)),this.calcLinePoints())}_getNonTransformedDimensions(){const t=super._getNonTransformedDimensions();return this.strokeLineCap==="butt"&&(this.width===0&&(t.y-=this.strokeWidth),this.height===0&&(t.x-=this.strokeWidth)),t}calcLinePoints(){const{x1:t,x2:e,y1:s,y2:r,width:i,height:n}=this,o=t<=e?-1:1,h=s<=r?-1:1;return{x1:o*i/2,x2:o*-i/2,y1:h*n/2,y2:h*-n/2}}_toSVG(){const{x1:t,x2:e,y1:s,y2:r}=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="'.concat(t,'" y1="').concat(s,'" x2="').concat(e,'" y2="').concat(r,`" />
`)]}static async fromElement(t,e,s){const r=Wt(t,this.ATTRIBUTE_NAMES,s),{x1:i=0,y1:n=0,x2:o=0,y2:h=0}=r;return new this([i,n,o,h],I(r,Ha))}static fromObject(t){let{x1:e,y1:s,x2:r,y2:i}=t,n=I(t,Ga);return this._fromObject(m(m({},n),{},{points:[e,s,r,i]}),{extraParam:"points"})}}p($t,"type","Line"),p($t,"cacheProperties",[...Xt,...vr]),p($t,"ATTRIBUTE_NAMES",qt.concat(vr)),T.setClass($t),T.setSVGClass($t);class Qt extends Z{static getDefaults(){return m(m({},super.getDefaults()),Qt.ownDefaults)}constructor(t){super(),Object.assign(this,Qt.ownDefaults),this.setOptions(t)}_render(t){const e=this.width/2,s=this.height/2;t.beginPath(),t.moveTo(-e,s),t.lineTo(0,-s),t.lineTo(e,s),t.closePath(),this._renderPaintInOrder(t)}_toSVG(){const t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',"".concat(-t," ").concat(e,",0 ").concat(-e,",").concat(t," ").concat(e),'" />']}}p(Qt,"type","Triangle"),p(Qt,"ownDefaults",{width:100,height:100}),T.setClass(Qt),T.setSVGClass(Qt);const pn=["rx","ry"];class Et extends Z{static getDefaults(){return m(m({},super.getDefaults()),Et.ownDefaults)}constructor(t){super(),Object.assign(this,Et.ownDefaults),this.setOptions(t)}_set(t,e){switch(super._set(t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this}getRx(){return this.get("rx")*this.get(ot)}getRy(){return this.get("ry")*this.get(gt)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...pn,...t])}_toSVG(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" rx="'.concat(this.rx,'" ry="').concat(this.ry,`" />
`)]}_render(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,xs,!1),t.restore(),this._renderPaintInOrder(t)}static async fromElement(t,e,s){const r=Wt(t,this.ATTRIBUTE_NAMES,s);return r.left=(r.left||0)-r.rx,r.top=(r.top||0)-r.ry,new this(r)}}function Na(a){if(!a)return[];const t=a.replace(/,/g," ").trim().split(/\s+/),e=[];for(let s=0;s<t.length;s+=2)e.push({x:parseFloat(t[s]),y:parseFloat(t[s+1])});return e}p(Et,"type","Ellipse"),p(Et,"cacheProperties",[...Xt,...pn]),p(Et,"ownDefaults",{rx:0,ry:0}),p(Et,"ATTRIBUTE_NAMES",[...qt,"cx","cy","rx","ry"]),T.setClass(Et),T.setSVGClass(Et);const Ua=["left","top"],mn={exactBoundingBox:!1};class yt extends Z{static getDefaults(){return m(m({},super.getDefaults()),yt.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),p(this,"strokeDiff",void 0),Object.assign(this,yt.ownDefaults),this.setOptions(e),this.points=t;const{left:s,top:r}=e;this.initialized=!0,this.setBoundingBox(!0),typeof s=="number"&&this.set(j,s),typeof r=="number"&&this.set(rt,r)}isOpen(){return!0}_projectStrokeOnPoints(t){return Lo(this.points,t,this.isOpen())}_calcDimensions(t){t=m({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,strokeLineCap:this.strokeLineCap,strokeLineJoin:this.strokeLineJoin,strokeMiterLimit:this.strokeMiterLimit,strokeUniform:this.strokeUniform,strokeWidth:this.strokeWidth},t||{});const e=this.exactBoundingBox?this._projectStrokeOnPoints(t).map(l=>l.projectedPoint):this.points;if(e.length===0)return{left:0,top:0,width:0,height:0,pathOffset:new _,strokeOffset:new _,strokeDiff:new _};const s=At(e),r=Fs(m(m({},t),{},{scaleX:1,scaleY:1})),i=At(this.points.map(l=>st(l,r,!0))),n=new _(this.scaleX,this.scaleY);let o=s.left+s.width/2,h=s.top+s.height/2;return this.exactBoundingBox&&(o-=h*Math.tan(H(this.skewX)),h-=o*Math.tan(H(this.skewY))),m(m({},s),{},{pathOffset:new _(o,h),strokeOffset:new _(i.left,i.top).subtract(new _(s.left,s.top)).multiply(n),strokeDiff:new _(s.width,s.height).subtract(new _(i.width,i.height)).multiply(n)})}_findCenterFromElement(){const t=At(this.points);return new _(t.left+t.width/2,t.top+t.height/2)}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{left:e,top:s,width:r,height:i,pathOffset:n,strokeOffset:o,strokeDiff:h}=this._calcDimensions();this.set({width:r,height:i,pathOffset:n,strokeOffset:o,strokeDiff:h}),t&&this.setPositionByOrigin(new _(e+r/2,s+i/2),k,k)}isStrokeAccountedForInDimensions(){return this.exactBoundingBox}_getNonTransformedDimensions(){return this.exactBoundingBox?new _(this.width,this.height):super._getNonTransformedDimensions()}_getTransformedDimensions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(this.exactBoundingBox){let n;if(Object.keys(t).some(o=>this.strokeUniform||this.constructor.layoutProperties.includes(o))){var e,s;const{width:o,height:h}=this._calcDimensions(t);n=new _((e=t.width)!==null&&e!==void 0?e:o,(s=t.height)!==null&&s!==void 0?s:h)}else{var r,i;n=new _((r=t.width)!==null&&r!==void 0?r:this.width,(i=t.height)!==null&&i!==void 0?i:this.height)}return n.multiply(new _(t.scaleX||this.scaleX,t.scaleY||this.scaleY))}return super._getTransformedDimensions(t)}_set(t,e){const s=this.initialized&&this[t]!==e,r=super._set(t,e);return this.exactBoundingBox&&s&&((t===ot||t===gt)&&this.strokeUniform&&this.constructor.layoutProperties.includes("strokeUniform")||this.constructor.layoutProperties.includes(t))&&this.setDimensions(),r}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject(t)),{},{points:this.points.map(e=>{let{x:s,y:r}=e;return{x:s,y:r}})})}_toSVG(){const t=[],e=this.pathOffset.x,s=this.pathOffset.y,r=E.NUM_FRACTION_DIGITS;for(let i=0,n=this.points.length;i<n;i++)t.push(B(this.points[i].x-e,r),",",B(this.points[i].y-s,r)," ");return["<".concat(this.constructor.type.toLowerCase()," "),"COMMON_PARTS",'points="'.concat(t.join(""),`" />
`)]}_render(t){const e=this.points.length,s=this.pathOffset.x,r=this.pathOffset.y;if(e&&!isNaN(this.points[e-1].y)){t.beginPath(),t.moveTo(this.points[0].x-s,this.points[0].y-r);for(let i=0;i<e;i++){const n=this.points[i];t.lineTo(n.x-s,n.y-r)}!this.isOpen()&&t.closePath(),this._renderPaintInOrder(t)}}complexity(){return this.points.length}static async fromElement(t,e,s){return new this(Na(t.getAttribute("points")),m(m({},I(Wt(t,this.ATTRIBUTE_NAMES,s),Ua)),e))}static fromObject(t){return this._fromObject(t,{extraParam:"points"})}}p(yt,"ownDefaults",mn),p(yt,"type","Polyline"),p(yt,"layoutProperties",[ve,ye,"strokeLineCap","strokeLineJoin","strokeMiterLimit","strokeWidth","strokeUniform","points"]),p(yt,"cacheProperties",[...Xt,"points"]),p(yt,"ATTRIBUTE_NAMES",[...qt]),T.setClass(yt),T.setSVGClass(yt);class hs extends yt{isOpen(){return!1}}p(hs,"ownDefaults",mn),p(hs,"type","Polygon"),T.setClass(hs),T.setSVGClass(hs);const vn=["fontSize","fontWeight","fontFamily","fontStyle"],yn=["underline","overline","linethrough"],_n=[...vn,"lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],xn=[..._n,...yn,"textBackgroundColor","direction"],qa=[...vn,...yn,nt,"strokeWidth",K,"deltaY","textBackgroundColor"],Ka={_reNewline:Cr,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:j,fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stroke:null,shadow:null,path:void 0,pathStartOffset:0,pathSide:j,pathAlign:"baseline",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,deltaY:0,direction:"ltr",CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2},wt="justify",Es="justify-left",Ge="justify-right",Ne="justify-center";class Cn extends Z{isEmptyStyles(t){if(!this.styles||t!==void 0&&!this.styles[t])return!0;const e=t===void 0?this.styles:{line:this.styles[t]};for(const s in e)for(const r in e[s])for(const i in e[s][r])return!1;return!0}styleHas(t,e){if(!this.styles||e!==void 0&&!this.styles[e])return!1;const s=e===void 0?this.styles:{0:this.styles[e]};for(const r in s)for(const i in s[r])if(s[r][i][t]!==void 0)return!0;return!1}cleanStyle(t){if(!this.styles)return!1;const e=this.styles;let s,r,i=0,n=!0,o=0;for(const h in e){s=0;for(const l in e[h]){const c=e[h][l]||{};i++,c[t]!==void 0?(r?c[t]!==r&&(n=!1):r=c[t],c[t]===this[t]&&delete c[t]):n=!1,Object.keys(c).length!==0?s++:delete e[h][l]}s===0&&delete e[h]}for(let h=0;h<this._textLines.length;h++)o+=this._textLines[h].length;n&&i===o&&(this[t]=r,this.removeStyle(t))}removeStyle(t){if(!this.styles)return;const e=this.styles;let s,r,i;for(r in e){for(i in s=e[r],s)delete s[i][t],Object.keys(s[i]).length===0&&delete s[i];Object.keys(s).length===0&&delete e[r]}}_extendStyles(t,e){const{lineIndex:s,charIndex:r}=this.get2DCursorLocation(t);this._getLineStyle(s)||this._setLineStyle(s);const i=Or(m(m({},this._getStyleDeclaration(s,r)),e),n=>n!==void 0);this._setStyleDeclaration(s,r,i)}getSelectionStyles(t,e,s){const r=[];for(let i=t;i<(e||t);i++)r.push(this.getStyleAtPosition(i,s));return r}getStyleAtPosition(t,e){const{lineIndex:s,charIndex:r}=this.get2DCursorLocation(t);return e?this.getCompleteStyleDeclaration(s,r):this._getStyleDeclaration(s,r)}setSelectionStyles(t,e,s){for(let r=e;r<(s||e);r++)this._extendStyles(r,t);this._forceClearCache=!0}_getStyleDeclaration(t,e){var s;const r=this.styles&&this.styles[t];return r&&(s=r[e])!==null&&s!==void 0?s:{}}getCompleteStyleDeclaration(t,e){return m(m({},xe(this,this.constructor._styleProperties)),this._getStyleDeclaration(t,e))}_setStyleDeclaration(t,e,s){this.styles[t][e]=s}_deleteStyleDeclaration(t,e){delete this.styles[t][e]}_getLineStyle(t){return!!this.styles[t]}_setLineStyle(t){this.styles[t]={}}_deleteLineStyle(t){delete this.styles[t]}}p(Cn,"_styleProperties",qa);const Ja=/  +/g,Za=/"/g;function Zs(a,t,e,s,r){return"		".concat(function(i,n){let{left:o,top:h,width:l,height:c}=n,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:E.NUM_FRACTION_DIGITS;const g=Je(K,i,!1),[d,f,v,y]=[o,h,l,c].map(x=>B(x,u));return"<rect ".concat(g,' x="').concat(d,'" y="').concat(f,'" width="').concat(v,'" height="').concat(y,'"></rect>')}(a,{left:t,top:e,width:s,height:r}),`
`)}const $a=["textAnchor","textDecoration","dx","dy","top","left","fontSize","strokeWidth"];let $s;class $ extends Cn{static getDefaults(){return m(m({},super.getDefaults()),$.ownDefaults)}constructor(t,e){super(),p(this,"__charBounds",[]),Object.assign(this,$.ownDefaults),this.setOptions(e),this.styles||(this.styles={}),this.text=t,this.initialized=!0,this.path&&this.setPathInfo(),this.initDimensions(),this.setCoords()}setPathInfo(){const t=this.path;t&&(t.segmentsInfo=hn(t.path))}_splitText(){const t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t}initDimensions(){this._splitText(),this._clearCache(),this.dirty=!0,this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),this.textAlign.includes(wt)&&this.enlargeSpaces()}enlargeSpaces(){let t,e,s,r,i,n,o;for(let h=0,l=this._textLines.length;h<l;h++)if((this.textAlign===wt||h!==l-1&&!this.isEndOfWrapping(h))&&(r=0,i=this._textLines[h],e=this.getLineWidth(h),e<this.width&&(o=this.textLines[h].match(this._reSpacesAndTabs)))){s=o.length,t=(this.width-e)/s;for(let c=0;c<=i.length;c++)n=this.__charBounds[h][c],this._reSpaceAndTab.test(i[c])?(n.width+=t,n.kernedWidth+=t,n.left+=r,r+=t):n.left+=r}}isEndOfWrapping(t){return t===this._textLines.length-1}missingNewlineOffset(t){return 1}get2DCursorLocation(t,e){const s=e?this._unwrappedTextLines:this._textLines;let r;for(r=0;r<s.length;r++){if(t<=s[r].length)return{lineIndex:r,charIndex:t};t-=s[r].length+this.missingNewlineOffset(r,e)}return{lineIndex:r-1,charIndex:s[r-1].length<t?s[r-1].length:t}}toString(){return"#<Text (".concat(this.complexity(),'): { "text": "').concat(this.text,'", "fontFamily": "').concat(this.fontFamily,'" }>')}_getCacheCanvasDimensions(){const t=super._getCacheCanvasDimensions(),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t}_render(t){const e=this.path;e&&!e.isNotVisible()&&e._render(t),this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")}_renderText(t){this.paintFirst===nt?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))}_setTextStyles(t,e,s){if(t.textBaseline="alphabetic",this.path)switch(this.pathAlign){case k:t.textBaseline="middle";break;case"ascender":t.textBaseline=rt;break;case"descender":t.textBaseline=or}t.font=this._getFontDeclaration(e,s)}calcTextWidth(){let t=this.getLineWidth(0);for(let e=1,s=this._textLines.length;e<s;e++){const r=this.getLineWidth(e);r>t&&(t=r)}return t}_renderTextLine(t,e,s,r,i,n){this._renderChars(t,e,s,r,i,n)}_renderTextLinesBackground(t){if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor"))return;const e=t.fillStyle,s=this._getLeftOffset();let r=this._getTopOffset();for(let i=0,n=this._textLines.length;i<n;i++){const o=this.getHeightOfLine(i);if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor",i)){r+=o;continue}const h=this._textLines[i].length,l=this._getLineLeftOffset(i);let c,u,g=0,d=0,f=this.getValueOfPropertyAt(i,0,"textBackgroundColor");for(let v=0;v<h;v++){const y=this.__charBounds[i][v];u=this.getValueOfPropertyAt(i,v,"textBackgroundColor"),this.path?(t.save(),t.translate(y.renderLeft,y.renderTop),t.rotate(y.angle),t.fillStyle=u,u&&t.fillRect(-y.width/2,-o/this.lineHeight*(1-this._fontSizeFraction),y.width,o/this.lineHeight),t.restore()):u!==f?(c=s+l+d,this.direction==="rtl"&&(c=this.width-c-g),t.fillStyle=f,f&&t.fillRect(c,r,g,o/this.lineHeight),d=y.left,g=y.width,f=u):g+=y.kernedWidth}u&&!this.path&&(c=s+l+d,this.direction==="rtl"&&(c=this.width-c-g),t.fillStyle=u,t.fillRect(c,r,g,o/this.lineHeight)),r+=o}t.fillStyle=e,this._removeShadow(t)}_measureChar(t,e,s,r){const i=ze.getFontCache(e),n=this._getFontDeclaration(e),o=s+t,h=s&&n===this._getFontDeclaration(r),l=e.fontSize/this.CACHE_FONT_SIZE;let c,u,g,d;if(s&&i[s]!==void 0&&(g=i[s]),i[t]!==void 0&&(d=c=i[t]),h&&i[o]!==void 0&&(u=i[o],d=u-g),c===void 0||g===void 0||u===void 0){const f=function(){return $s||($s=dt({width:0,height:0}).getContext("2d")),$s}();this._setTextStyles(f,e,!0),c===void 0&&(d=c=f.measureText(t).width,i[t]=c),g===void 0&&h&&s&&(g=f.measureText(s).width,i[s]=g),h&&u===void 0&&(u=f.measureText(o).width,i[o]=u,d=u-g)}return{width:c*l,kernedWidth:d*l}}getHeightOfChar(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")}measureLine(t){const e=this._measureLine(t);return this.charSpacing!==0&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e}_measureLine(t){let e,s,r=0;const i=this.pathSide===z,n=this.path,o=this._textLines[t],h=o.length,l=new Array(h);this.__charBounds[t]=l;for(let c=0;c<h;c++){const u=o[c];s=this._getGraphemeBox(u,t,c,e),l[c]=s,r+=s.kernedWidth,e=u}if(l[h]={left:s?s.left+s.width:0,width:0,kernedWidth:0,height:this.fontSize,deltaY:0},n&&n.segmentsInfo){let c=0;const u=n.segmentsInfo[n.segmentsInfo.length-1].length;switch(this.textAlign){case j:c=i?u-r:0;break;case k:c=(u-r)/2;break;case z:c=i?0:u-r}c+=this.pathStartOffset*(i?-1:1);for(let g=i?h-1:0;i?g>=0:g<h;i?g--:g++)s=l[g],c>u?c%=u:c<0&&(c+=u),this._setGraphemeOnPath(c,s),c+=s.kernedWidth}return{width:r,numOfSpaces:0}}_setGraphemeOnPath(t,e){const s=t+e.kernedWidth/2,r=this.path,i=Sa(r.path,s,r.segmentsInfo);e.renderLeft=i.x-r.pathOffset.x,e.renderTop=i.y-r.pathOffset.y,e.angle=i.angle+(this.pathSide===z?Math.PI:0)}_getGraphemeBox(t,e,s,r,i){const n=this.getCompleteStyleDeclaration(e,s),o=r?this.getCompleteStyleDeclaration(e,s-1):{},h=this._measureChar(t,n,r,o);let l,c=h.kernedWidth,u=h.width;this.charSpacing!==0&&(l=this._getWidthOfCharSpacing(),u+=l,c+=l);const g={width:u,left:0,height:n.fontSize,kernedWidth:c,deltaY:n.deltaY};if(s>0&&!i){const d=this.__charBounds[e][s-1];g.left=d.left+d.width+h.kernedWidth-h.width}return g}getHeightOfLine(t){if(this.__lineHeights[t])return this.__lineHeights[t];let e=this.getHeightOfChar(t,0);for(let s=1,r=this._textLines[t].length;s<r;s++)e=Math.max(this.getHeightOfChar(t,s),e);return this.__lineHeights[t]=e*this.lineHeight*this._fontSizeMult}calcTextHeight(){let t,e=0;for(let s=0,r=this._textLines.length;s<r;s++)t=this.getHeightOfLine(s),e+=s===r-1?t/this.lineHeight:t;return e}_getLeftOffset(){return this.direction==="ltr"?-this.width/2:this.width/2}_getTopOffset(){return-this.height/2}_renderTextCommon(t,e){t.save();let s=0;const r=this._getLeftOffset(),i=this._getTopOffset();for(let n=0,o=this._textLines.length;n<o;n++){const h=this.getHeightOfLine(n),l=h/this.lineHeight,c=this._getLineLeftOffset(n);this._renderTextLine(e,t,this._textLines[n],r+c,i+s+l,n),s+=h}t.restore()}_renderTextFill(t){(this.fill||this.styleHas(K))&&this._renderTextCommon(t,"fillText")}_renderTextStroke(t){(this.stroke&&this.strokeWidth!==0||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())}_renderChars(t,e,s,r,i,n){const o=this.getHeightOfLine(n),h=this.textAlign.includes(wt),l=this.path,c=!h&&this.charSpacing===0&&this.isEmptyStyles(n)&&!l,u=this.direction==="ltr",g=this.direction==="ltr"?1:-1,d=e.direction;let f,v,y,x,C,w="",S=0;if(e.save(),d!==this.direction&&(e.canvas.setAttribute("dir",u?"ltr":"rtl"),e.direction=u?"ltr":"rtl",e.textAlign=u?j:z),i-=o*this._fontSizeFraction/this.lineHeight,c)return this._renderChar(t,e,n,0,s.join(""),r,i),void e.restore();for(let b=0,D=s.length-1;b<=D;b++)x=b===D||this.charSpacing||l,w+=s[b],y=this.__charBounds[n][b],S===0?(r+=g*(y.kernedWidth-y.width),S+=y.width):S+=y.kernedWidth,h&&!x&&this._reSpaceAndTab.test(s[b])&&(x=!0),x||(f=f||this.getCompleteStyleDeclaration(n,b),v=this.getCompleteStyleDeclaration(n,b+1),x=Fr(f,v,!1)),x&&(l?(e.save(),e.translate(y.renderLeft,y.renderTop),e.rotate(y.angle),this._renderChar(t,e,n,b,w,-S/2,0),e.restore()):(C=r,this._renderChar(t,e,n,b,w,C,i)),w="",f=v,r+=g*S,S=0);e.restore()}_applyPatternGradientTransformText(t){const e=this.width+this.strokeWidth,s=this.height+this.strokeWidth,r=dt({width:e,height:s}),i=r.getContext("2d");return r.width=e,r.height=s,i.beginPath(),i.moveTo(0,0),i.lineTo(e,0),i.lineTo(e,s),i.lineTo(0,s),i.closePath(),i.translate(e/2,s/2),i.fillStyle=t.toLive(i),this._applyPatternGradientTransform(i,t),i.fill(),i.createPattern(r,"no-repeat")}handleFiller(t,e,s){let r,i;return ut(s)?s.gradientUnits==="percentage"||s.gradientTransform||s.patternTransform?(r=-this.width/2,i=-this.height/2,t.translate(r,i),t[e]=this._applyPatternGradientTransformText(s),{offsetX:r,offsetY:i}):(t[e]=s.toLive(t),this._applyPatternGradientTransform(t,s)):(t[e]=s,{offsetX:0,offsetY:0})}_setStrokeStyles(t,e){let{stroke:s,strokeWidth:r}=e;return t.lineWidth=r,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",s)}_setFillStyles(t,e){let{fill:s}=e;return this.handleFiller(t,"fillStyle",s)}_renderChar(t,e,s,r,i,n,o){const h=this._getStyleDeclaration(s,r),l=this.getCompleteStyleDeclaration(s,r),c=t==="fillText"&&l.fill,u=t==="strokeText"&&l.stroke&&l.strokeWidth;if(u||c){if(e.save(),e.font=this._getFontDeclaration(l),h.textBackgroundColor&&this._removeShadow(e),h.deltaY&&(o+=h.deltaY),c){const g=this._setFillStyles(e,l);e.fillText(i,n-g.offsetX,o-g.offsetY)}if(u){const g=this._setStrokeStyles(e,l);e.strokeText(i,n-g.offsetX,o-g.offsetY)}e.restore()}}setSuperscript(t,e){this._setScript(t,e,this.superscript)}setSubscript(t,e){this._setScript(t,e,this.subscript)}_setScript(t,e,s){const r=this.get2DCursorLocation(t,!0),i=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"fontSize"),n=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"deltaY"),o={fontSize:i*s.size,deltaY:n+i*s.baseline};this.setSelectionStyles(o,t,e)}_getLineLeftOffset(t){const e=this.getLineWidth(t),s=this.width-e,r=this.textAlign,i=this.direction,n=this.isEndOfWrapping(t);let o=0;return r===wt||r===Ne&&!n||r===Ge&&!n||r===Es&&!n?0:(r===k&&(o=s/2),r===z&&(o=s),r===Ne&&(o=s/2),r===Ge&&(o=s),i==="rtl"&&(r===z||r===wt||r===Ge?o=0:r===j||r===Es?o=-s:r!==k&&r!==Ne||(o=-s/2)),o)}_clearCache(){this._forceClearCache=!1,this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]}getLineWidth(t){if(this.__lineWidths[t]!==void 0)return this.__lineWidths[t];const{width:e}=this.measureLine(t);return this.__lineWidths[t]=e,e}_getWidthOfCharSpacing(){return this.charSpacing!==0?this.fontSize*this.charSpacing/1e3:0}getValueOfPropertyAt(t,e,s){var r;return(r=this._getStyleDeclaration(t,e)[s])!==null&&r!==void 0?r:this[s]}_renderTextDecoration(t,e){if(!this[e]&&!this.styleHas(e))return;let s=this._getTopOffset();const r=this._getLeftOffset(),i=this.path,n=this._getWidthOfCharSpacing(),o=this.offsets[e];for(let h=0,l=this._textLines.length;h<l;h++){const c=this.getHeightOfLine(h);if(!this[e]&&!this.styleHas(e,h)){s+=c;continue}const u=this._textLines[h],g=c/this.lineHeight,d=this._getLineLeftOffset(h);let f,v,y=0,x=0,C=this.getValueOfPropertyAt(h,0,e),w=this.getValueOfPropertyAt(h,0,K);const S=s+g*(1-this._fontSizeFraction);let b=this.getHeightOfChar(h,0),D=this.getValueOfPropertyAt(h,0,"deltaY");for(let O=0,F=u.length;O<F;O++){const L=this.__charBounds[h][O];f=this.getValueOfPropertyAt(h,O,e),v=this.getValueOfPropertyAt(h,O,K);const q=this.getHeightOfChar(h,O),V=this.getValueOfPropertyAt(h,O,"deltaY");if(i&&f&&v)t.save(),t.fillStyle=w,t.translate(L.renderLeft,L.renderTop),t.rotate(L.angle),t.fillRect(-L.kernedWidth/2,o*q+V,L.kernedWidth,this.fontSize/15),t.restore();else if((f!==C||v!==w||q!==b||V!==D)&&x>0){let P=r+d+y;this.direction==="rtl"&&(P=this.width-P-x),C&&w&&(t.fillStyle=w,t.fillRect(P,S+o*b+D,x,this.fontSize/15)),y=L.left,x=L.width,C=f,w=v,b=q,D=V}else x+=L.kernedWidth}let M=r+d+y;this.direction==="rtl"&&(M=this.width-M-x),t.fillStyle=v,f&&v&&t.fillRect(M,S+o*b+D,x-n,this.fontSize/15),s+=c}this._removeShadow(t)}_getFontDeclaration(){let{fontFamily:t=this.fontFamily,fontStyle:e=this.fontStyle,fontWeight:s=this.fontWeight,fontSize:r=this.fontSize}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0;const n=t.includes("'")||t.includes('"')||t.includes(",")||$.genericFonts.includes(t.toLowerCase())?t:'"'.concat(t,'"');return[e,s,"".concat(i?this.CACHE_FONT_SIZE:r,"px"),n].join(" ")}render(t){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._forceClearCache&&this.initDimensions(),super.render(t)))}graphemeSplit(t){return Ar(t)}_splitTextIntoLines(t){const e=t.split(this._reNewline),s=new Array(e.length),r=[`
`];let i=[];for(let n=0;n<e.length;n++)s[n]=this.graphemeSplit(e[n]),i=i.concat(s[n],r);return i.pop(),{_unwrappedLines:s,lines:e,graphemeText:i,graphemeLines:s}}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject([...xn,...t])),{},{styles:Xo(this.styles,this.text)},this.path?{path:this.path.toObject()}:{})}set(t,e){const{textLayoutProperties:s}=this.constructor;super.set(t,e);let r=!1,i=!1;if(typeof t=="object")for(const n in t)n==="path"&&this.setPathInfo(),r=r||s.includes(n),i=i||n==="path";else r=s.includes(t),i=t==="path";return i&&this.setPathInfo(),r&&this.initialized&&(this.initDimensions(),this.setCoords()),this}complexity(){return 1}static async fromElement(t,e,s){const r=Wt(t,$.ATTRIBUTE_NAMES,s),i=m(m({},e),r),{textAnchor:n=j,textDecoration:o="",dx:h=0,dy:l=0,top:c=0,left:u=0,fontSize:g=xr,strokeWidth:d=1}=i,f=I(i,$a),v=new this((t.textContent||"").replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," "),m({left:u+h,top:c+l,underline:o.includes("underline"),overline:o.includes("overline"),linethrough:o.includes("line-through"),strokeWidth:0,fontSize:g},f)),y=v.getScaledHeight()/v.height,x=((v.height+v.strokeWidth)*v.lineHeight-v.height)*y,C=v.getScaledHeight()+x;let w=0;return n===k&&(w=v.getScaledWidth()/2),n===z&&(w=v.getScaledWidth()),v.set({left:v.left-w,top:v.top-(C-v.fontSize*(.07+v._fontSizeFraction))/v.lineHeight,strokeWidth:d}),v}static fromObject(t){return this._fromObject(m(m({},t),{},{styles:Wo(t.styles||{},t.text)}),{extraParam:"text"})}}p($,"textLayoutProperties",_n),p($,"cacheProperties",[...Xt,...xn]),p($,"ownDefaults",Ka),p($,"type","Text"),p($,"genericFonts",["serif","sans-serif","monospace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","math","emoji","fangsong"]),p($,"ATTRIBUTE_NAMES",qt.concat("x","y","dx","dy","font-family","font-style","font-weight","font-size","letter-spacing","text-decoration","text-anchor")),$i($,[class extends zi{_toSVG(){const a=this._getSVGLeftTopOffsets(),t=this._getSVGTextAndBg(a.textTop,a.textLeft);return this._wrapSVGTextAndBg(t)}toSVG(a){const t=this._createBaseSVGMarkup(this._toSVG(),{reviver:a,noStyle:!0,withShadow:!0}),e=this.path;return e?t+e._createBaseSVGMarkup(e._toSVG(),{reviver:a,withShadow:!0,additionalTransform:Ke(this.calcOwnMatrix())}):t}_getSVGLeftTopOffsets(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}}_wrapSVGTextAndBg(a){let{textBgRects:t,textSpans:e}=a;const s=this.getSvgTextDecoration(this);return[t.join(""),'		<text xml:space="preserve" ',this.fontFamily?'font-family="'.concat(this.fontFamily.replace(Za,"'"),'" '):"",this.fontSize?'font-size="'.concat(this.fontSize,'" '):"",this.fontStyle?'font-style="'.concat(this.fontStyle,'" '):"",this.fontWeight?'font-weight="'.concat(this.fontWeight,'" '):"",s?'text-decoration="'.concat(s,'" '):"",this.direction==="rtl"?'direction="'.concat(this.direction,'" '):"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",e.join(""),`</text>
`]}_getSVGTextAndBg(a,t){const e=[],s=[];let r,i=a;this.backgroundColor&&s.push(...Zs(this.backgroundColor,-this.width/2,-this.height/2,this.width,this.height));for(let n=0,o=this._textLines.length;n<o;n++)r=this._getLineLeftOffset(n),this.direction==="rtl"&&(r+=this.width),(this.textBackgroundColor||this.styleHas("textBackgroundColor",n))&&this._setSVGTextLineBg(s,n,t+r,i),this._setSVGTextLineText(e,n,t+r,i),i+=this.getHeightOfLine(n);return{textSpans:e,textBgRects:s}}_createTextCharSpan(a,t,e,s,r){const i=E.NUM_FRACTION_DIGITS,n=this.getSvgSpanStyles(t,a!==a.trim()||!!a.match(Ja)),o=n?'style="'.concat(n,'"'):"",h=t.deltaY,l=h?' dy="'.concat(B(h,i),'" '):"",{angle:c,renderLeft:u,renderTop:g,width:d}=r;let f="";if(u!==void 0){const v=d/2;c&&(f=' rotate="'.concat(B(It(c),i),'"'));const y=_e({angle:It(c)});y[4]=u,y[5]=g;const x=new _(-v,0).transform(y);e=x.x,s=x.y}return'<tspan x="'.concat(B(e,i),'" y="').concat(B(s,i),'" ').concat(l).concat(f).concat(o,">").concat(Ro(a),"</tspan>")}_setSVGTextLineText(a,t,e,s){const r=this.getHeightOfLine(t),i=this.textAlign.includes(wt),n=this._textLines[t];let o,h,l,c,u,g="",d=0;s+=r*(1-this._fontSizeFraction)/this.lineHeight;for(let f=0,v=n.length-1;f<=v;f++)u=f===v||this.charSpacing||this.path,g+=n[f],l=this.__charBounds[t][f],d===0?(e+=l.kernedWidth-l.width,d+=l.width):d+=l.kernedWidth,i&&!u&&this._reSpaceAndTab.test(n[f])&&(u=!0),u||(o=o||this.getCompleteStyleDeclaration(t,f),h=this.getCompleteStyleDeclaration(t,f+1),u=Fr(o,h,!0)),u&&(c=this._getStyleDeclaration(t,f),a.push(this._createTextCharSpan(g,c,e,s,l)),g="",o=h,this.direction==="rtl"?e-=d:e+=d,d=0)}_setSVGTextLineBg(a,t,e,s){const r=this._textLines[t],i=this.getHeightOfLine(t)/this.lineHeight;let n,o=0,h=0,l=this.getValueOfPropertyAt(t,0,"textBackgroundColor");for(let c=0;c<r.length;c++){const{left:u,width:g,kernedWidth:d}=this.__charBounds[t][c];n=this.getValueOfPropertyAt(t,c,"textBackgroundColor"),n!==l?(l&&a.push(...Zs(l,e+h,s,o,i)),h=u,o=g,l=n):o+=d}n&&a.push(...Zs(l,e+h,s,o,i))}_getSVGLineTopOffset(a){let t,e=0;for(t=0;t<a;t++)e+=this.getHeightOfLine(t);const s=this.getHeightOfLine(t);return{lineTop:e,offset:(this._fontSizeMult-this._fontSizeFraction)*s/(this.lineHeight*this._fontSizeMult)}}getSvgStyles(a){return"".concat(super.getSvgStyles(a)," white-space: pre;")}getSvgSpanStyles(a,t){const{fontFamily:e,strokeWidth:s,stroke:r,fill:i,fontSize:n,fontStyle:o,fontWeight:h,deltaY:l}=a,c=this.getSvgTextDecoration(a);return[r?Je(nt,r):"",s?"stroke-width: ".concat(s,"; "):"",e?"font-family: ".concat(e.includes("'")||e.includes('"')?e:"'".concat(e,"'"),"; "):"",n?"font-size: ".concat(n,"px; "):"",o?"font-style: ".concat(o,"; "):"",h?"font-weight: ".concat(h,"; "):"",c&&"text-decoration: ".concat(c,"; "),i?Je(K,i):"",l?"baseline-shift: ".concat(-l,"; "):"",t?"white-space: pre; ":""].join("")}getSvgTextDecoration(a){return["overline","underline","line-through"].filter(t=>a[t.replace("-","")]).join(" ")}}]),T.setClass($),T.setSVGClass($);class Qa{constructor(t){p(this,"target",void 0),p(this,"__mouseDownInPlace",!1),p(this,"__dragStartFired",!1),p(this,"__isDraggingOver",!1),p(this,"__dragStartSelection",void 0),p(this,"__dragImageDisposer",void 0),p(this,"_dispose",void 0),this.target=t;const e=[this.target.on("dragenter",this.dragEnterHandler.bind(this)),this.target.on("dragover",this.dragOverHandler.bind(this)),this.target.on("dragleave",this.dragLeaveHandler.bind(this)),this.target.on("dragend",this.dragEndHandler.bind(this)),this.target.on("drop",this.dropHandler.bind(this))];this._dispose=()=>{e.forEach(s=>s()),this._dispose=void 0}}isPointerOverSelection(t){const e=this.target,s=e.getSelectionStartFromPointer(t);return e.isEditing&&s>=e.selectionStart&&s<=e.selectionEnd&&e.selectionStart<e.selectionEnd}start(t){return this.__mouseDownInPlace=this.isPointerOverSelection(t)}isActive(){return this.__mouseDownInPlace}end(t){const e=this.isActive();return e&&!this.__dragStartFired&&(this.target.setCursorByClick(t),this.target.initDelayedCursor(!0)),this.__mouseDownInPlace=!1,this.__dragStartFired=!1,this.__isDraggingOver=!1,e}getDragStartSelection(){return this.__dragStartSelection}setDragImage(t,e){var s;let{selectionStart:r,selectionEnd:i}=e;const n=this.target,o=n.canvas,h=new _(n.flipX?-1:1,n.flipY?-1:1),l=n._getCursorBoundaries(r),c=new _(l.left+l.leftOffset,l.top+l.topOffset).multiply(h).transform(n.calcTransformMatrix()),u=o.getScenePoint(t).subtract(c),g=n.getCanvasRetinaScaling(),d=n.getBoundingRect(),f=c.subtract(new _(d.left,d.top)),v=o.viewportTransform,y=f.add(u).transform(v,!0),x=n.backgroundColor,C=Pr(n.styles);n.backgroundColor="";const w={stroke:"transparent",fill:"transparent",textBackgroundColor:"transparent"};n.setSelectionStyles(w,0,r),n.setSelectionStyles(w,i,n.text.length),n.dirty=!0;const S=n.toCanvasElement({enableRetinaScaling:o.enableRetinaScaling,viewportTransform:!0});n.backgroundColor=x,n.styles=C,n.dirty=!0,pr(S,{position:"fixed",left:"".concat(-S.width,"px"),border:it,width:"".concat(S.width/g,"px"),height:"".concat(S.height/g,"px")}),this.__dragImageDisposer&&this.__dragImageDisposer(),this.__dragImageDisposer=()=>{S.remove()},_t(t.target||this.target.hiddenTextarea).body.appendChild(S),(s=t.dataTransfer)===null||s===void 0||s.setDragImage(S,y.x,y.y)}onDragStart(t){this.__dragStartFired=!0;const e=this.target,s=this.isActive();if(s&&t.dataTransfer){const r=this.__dragStartSelection={selectionStart:e.selectionStart,selectionEnd:e.selectionEnd},i=e._text.slice(r.selectionStart,r.selectionEnd).join(""),n=m({text:e.text,value:i},r);t.dataTransfer.setData("text/plain",i),t.dataTransfer.setData("application/fabric",JSON.stringify({value:i,styles:e.getSelectionStyles(r.selectionStart,r.selectionEnd,!0)})),t.dataTransfer.effectAllowed="copyMove",this.setDragImage(t,n)}return e.abortCursorAnimation(),s}canDrop(t){if(this.target.editable&&!this.target.getActiveControl()&&!t.defaultPrevented){if(this.isActive()&&this.__dragStartSelection){const e=this.target.getSelectionStartFromPointer(t),s=this.__dragStartSelection;return e<s.selectionStart||e>s.selectionEnd}return!0}return!1}targetCanDrop(t){return this.target.canDrop(t)}dragEnterHandler(t){let{e}=t;const s=this.targetCanDrop(e);!this.__isDraggingOver&&s&&(this.__isDraggingOver=!0)}dragOverHandler(t){const{e}=t,s=this.targetCanDrop(e);!this.__isDraggingOver&&s?this.__isDraggingOver=!0:this.__isDraggingOver&&!s&&(this.__isDraggingOver=!1),this.__isDraggingOver&&(e.preventDefault(),t.canDrop=!0,t.dropTarget=this.target)}dragLeaveHandler(){(this.__isDraggingOver||this.isActive())&&(this.__isDraggingOver=!1)}dropHandler(t){var e;const{e:s}=t,r=s.defaultPrevented;this.__isDraggingOver=!1,s.preventDefault();let i=(e=s.dataTransfer)===null||e===void 0?void 0:e.getData("text/plain");if(i&&!r){const n=this.target,o=n.canvas;let h=n.getSelectionStartFromPointer(s);const{styles:l}=s.dataTransfer.types.includes("application/fabric")?JSON.parse(s.dataTransfer.getData("application/fabric")):{},c=i[Math.max(0,i.length-1)],u=0;if(this.__dragStartSelection){const g=this.__dragStartSelection.selectionStart,d=this.__dragStartSelection.selectionEnd;h>g&&h<=d?h=g:h>d&&(h-=d-g),n.removeChars(g,d),delete this.__dragStartSelection}n._reNewline.test(c)&&(n._reNewline.test(n._text[h])||h===n._text.length)&&(i=i.trimEnd()),t.didDrop=!0,t.dropTarget=n,n.insertChars(i,l,h),o.setActiveObject(n),n.enterEditing(s),n.selectionStart=Math.min(h+u,n._text.length),n.selectionEnd=Math.min(n.selectionStart+i.length,n._text.length),n.hiddenTextarea.value=n.text,n._updateTextarea(),n.hiddenTextarea.focus(),n.fire(Cs,{index:h+u,action:"drop"}),o.fire("text:changed",{target:n}),o.contextTopDirty=!0,o.requestRenderAll()}}dragEndHandler(t){let{e}=t;if(this.isActive()&&this.__dragStartFired&&this.__dragStartSelection){var s;const r=this.target,i=this.target.canvas,{selectionStart:n,selectionEnd:o}=this.__dragStartSelection,h=((s=e.dataTransfer)===null||s===void 0?void 0:s.dropEffect)||it;h===it?(r.selectionStart=n,r.selectionEnd=o,r._updateTextarea(),r.hiddenTextarea.focus()):(r.clearContextTop(),h==="move"&&(r.removeChars(n,o),r.selectionStart=r.selectionEnd=n,r.hiddenTextarea&&(r.hiddenTextarea.value=r.text),r._updateTextarea(),r.fire(Cs,{index:n,action:"dragend"}),i.fire("text:changed",{target:r}),i.requestRenderAll()),r.exitEditing())}this.__dragImageDisposer&&this.__dragImageDisposer(),delete this.__dragImageDisposer,delete this.__dragStartSelection,this.__isDraggingOver=!1}dispose(){this._dispose&&this._dispose()}}const vi=/[ \n\.,;!\?\-]/;class th extends ${constructor(){super(...arguments),p(this,"_currentCursorOpacity",1)}initBehavior(){this._tick=this._tick.bind(this),this._onTickComplete=this._onTickComplete.bind(this),this.updateSelectionOnMouseMove=this.updateSelectionOnMouseMove.bind(this)}onDeselect(t){return this.isEditing&&this.exitEditing(),this.selected=!1,super.onDeselect(t)}_animateCursor(t){let{toValue:e,duration:s,delay:r,onComplete:i}=t;return Ui({startValue:this._currentCursorOpacity,endValue:e,duration:s,delay:r,onComplete:i,abort:()=>!this.canvas||this.selectionStart!==this.selectionEnd,onChange:n=>{this._currentCursorOpacity=n,this.renderCursorOrSelection()}})}_tick(t){this._currentTickState=this._animateCursor({toValue:0,duration:this.cursorDuration/2,delay:Math.max(t||0,100),onComplete:this._onTickComplete})}_onTickComplete(){var t;(t=this._currentTickCompleteState)===null||t===void 0||t.abort(),this._currentTickCompleteState=this._animateCursor({toValue:1,duration:this.cursorDuration,onComplete:this._tick})}initDelayedCursor(t){this.abortCursorAnimation(),this._tick(t?0:this.cursorDelay)}abortCursorAnimation(){let t=!1;[this._currentTickState,this._currentTickCompleteState].forEach(e=>{e&&!e.isDone()&&(t=!0,e.abort())}),this._currentCursorOpacity=1,t&&this.clearContextTop()}restartCursorIfNeeded(){[this._currentTickState,this._currentTickCompleteState].some(t=>!t||t.isDone())&&this.initDelayedCursor()}selectAll(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this}cmdAll(){this.selectAll(),this.renderCursorOrSelection()}getSelectedText(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")}findWordBoundaryLeft(t){let e=0,s=t-1;if(this._reSpace.test(this._text[s]))for(;this._reSpace.test(this._text[s]);)e++,s--;for(;/\S/.test(this._text[s])&&s>-1;)e++,s--;return t-e}findWordBoundaryRight(t){let e=0,s=t;if(this._reSpace.test(this._text[s]))for(;this._reSpace.test(this._text[s]);)e++,s++;for(;/\S/.test(this._text[s])&&s<this._text.length;)e++,s++;return t+e}findLineBoundaryLeft(t){let e=0,s=t-1;for(;!/\n/.test(this._text[s])&&s>-1;)e++,s--;return t-e}findLineBoundaryRight(t){let e=0,s=t;for(;!/\n/.test(this._text[s])&&s<this._text.length;)e++,s++;return t+e}searchWordBoundary(t,e){const s=this._text;let r=t>0&&this._reSpace.test(s[t])&&(e===-1||!Cr.test(s[t-1]))?t-1:t,i=s[r];for(;r>0&&r<s.length&&!vi.test(i);)r+=e,i=s[r];return e===-1&&vi.test(i)&&r++,r}selectWord(t){var e;t=(e=t)!==null&&e!==void 0?e:this.selectionStart;const s=this.searchWordBoundary(t,-1),r=Math.max(s,this.searchWordBoundary(t,1));this.selectionStart=s,this.selectionEnd=r,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()}selectLine(t){var e;t=(e=t)!==null&&e!==void 0?e:this.selectionStart;const s=this.findLineBoundaryLeft(t),r=this.findLineBoundaryRight(t);this.selectionStart=s,this.selectionEnd=r,this._fireSelectionChanged(),this._updateTextarea()}enterEditing(t){!this.isEditing&&this.editable&&(this.enterEditingImpl(),this.fire("editing:entered",t?{e:t}:void 0),this._fireSelectionChanged(),this.canvas&&(this.canvas.fire("text:editing:entered",{target:this,e:t}),this.canvas.requestRenderAll()))}enterEditingImpl(){this.canvas&&(this.canvas.calcOffset(),this.canvas.textEditingManager.exitTextEditing()),this.isEditing=!0,this.initHiddenTextarea(),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick()}updateSelectionOnMouseMove(t){if(this.getActiveControl())return;const e=this.hiddenTextarea;_t(e).activeElement!==e&&e.focus();const s=this.getSelectionStartFromPointer(t),r=this.selectionStart,i=this.selectionEnd;(s===this.__selectionStartOnMouseDown&&r!==i||r!==s&&i!==s)&&(s>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=s):(this.selectionStart=s,this.selectionEnd=this.__selectionStartOnMouseDown),this.selectionStart===r&&this.selectionEnd===i||(this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}_setEditingProps(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0}fromStringToGraphemeSelection(t,e,s){const r=s.slice(0,t),i=this.graphemeSplit(r).length;if(t===e)return{selectionStart:i,selectionEnd:i};const n=s.slice(t,e);return{selectionStart:i,selectionEnd:i+this.graphemeSplit(n).length}}fromGraphemeToStringSelection(t,e,s){const r=s.slice(0,t).join("").length;return t===e?{selectionStart:r,selectionEnd:r}:{selectionStart:r,selectionEnd:r+s.slice(t,e).join("").length}}_updateTextarea(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){const t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}}updateFromTextArea(){if(!this.hiddenTextarea)return;this.cursorOffsetCache={};const t=this.hiddenTextarea;this.text=t.value,this.set("dirty",!0),this.initDimensions(),this.setCoords();const e=this.fromStringToGraphemeSelection(t.selectionStart,t.selectionEnd,t.value);this.selectionEnd=this.selectionStart=e.selectionEnd,this.inCompositionMode||(this.selectionStart=e.selectionStart),this.updateTextareaPosition()}updateTextareaPosition(){if(this.selectionStart===this.selectionEnd){const t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}}_calcTextareaPosition(){if(!this.canvas)return{left:"1px",top:"1px"};const t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),s=this.get2DCursorLocation(t),r=s.lineIndex,i=s.charIndex,n=this.getValueOfPropertyAt(r,i,"fontSize")*this.lineHeight,o=e.leftOffset,h=this.getCanvasRetinaScaling(),l=this.canvas.upperCanvasEl,c=l.width/h,u=l.height/h,g=c-n,d=u-n,f=new _(e.left+o,e.top+e.topOffset+n).transform(this.calcTransformMatrix()).transform(this.canvas.viewportTransform).multiply(new _(l.clientWidth/c,l.clientHeight/u));return f.x<0&&(f.x=0),f.x>g&&(f.x=g),f.y<0&&(f.y=0),f.y>d&&(f.y=d),f.x+=this.canvas._offset.left,f.y+=this.canvas._offset.top,{left:"".concat(f.x,"px"),top:"".concat(f.y,"px"),fontSize:"".concat(n,"px"),charHeight:n}}_saveEditingProps(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}}_restoreEditingProps(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor||this.canvas.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor||this.canvas.moveCursor),delete this._savedProps)}_exitEditing(){const t=this.hiddenTextarea;this.selected=!1,this.isEditing=!1,t&&(t.blur&&t.blur(),t.parentNode&&t.parentNode.removeChild(t)),this.hiddenTextarea=null,this.abortCursorAnimation(),this.selectionStart!==this.selectionEnd&&this.clearContextTop()}exitEditingImpl(){this._exitEditing(),this.selectionEnd=this.selectionStart,this._restoreEditingProps(),this._forceClearCache&&(this.initDimensions(),this.setCoords())}exitEditing(){const t=this._textBeforeEdit!==this.text;return this.exitEditingImpl(),this.fire("editing:exited"),t&&this.fire(bs),this.canvas&&(this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this}_removeExtraneousStyles(){for(const t in this.styles)this._textLines[t]||delete this.styles[t]}removeStyleFromTo(t,e){const{lineIndex:s,charIndex:r}=this.get2DCursorLocation(t,!0),{lineIndex:i,charIndex:n}=this.get2DCursorLocation(e,!0);if(s!==i){if(this.styles[s])for(let o=r;o<this._unwrappedTextLines[s].length;o++)delete this.styles[s][o];if(this.styles[i])for(let o=n;o<this._unwrappedTextLines[i].length;o++){const h=this.styles[i][o];h&&(this.styles[s]||(this.styles[s]={}),this.styles[s][r+o-n]=h)}for(let o=s+1;o<=i;o++)delete this.styles[o];this.shiftLineStyles(i,s-i)}else if(this.styles[s]){const o=this.styles[s],h=n-r;for(let l=r;l<n;l++)delete o[l];for(const l in this.styles[s]){const c=parseInt(l,10);c>=n&&(o[c-h]=o[l],delete o[l])}}}shiftLineStyles(t,e){const s=Object.assign({},this.styles);for(const r in this.styles){const i=parseInt(r,10);i>t&&(this.styles[i+e]=s[i],s[i-e]||delete this.styles[i])}}insertNewlineStyleObject(t,e,s,r){const i={},n=this._unwrappedTextLines[t].length,o=n===e;let h=!1;s||(s=1),this.shiftLineStyles(t,s);const l=this.styles[t]?this.styles[t][e===0?e:e-1]:void 0;for(const u in this.styles[t]){const g=parseInt(u,10);g>=e&&(h=!0,i[g-e]=this.styles[t][u],o&&e===0||delete this.styles[t][u])}let c=!1;for(h&&!o&&(this.styles[t+s]=i,c=!0),(c||n>e)&&s--;s>0;)r&&r[s-1]?this.styles[t+s]={0:m({},r[s-1])}:l?this.styles[t+s]={0:m({},l)}:delete this.styles[t+s],s--;this._forceClearCache=!0}insertCharStyleObject(t,e,s,r){this.styles||(this.styles={});const i=this.styles[t],n=i?m({},i):{};s||(s=1);for(const h in n){const l=parseInt(h,10);l>=e&&(i[l+s]=n[l],n[l-s]||delete i[l])}if(this._forceClearCache=!0,r){for(;s--;)Object.keys(r[s]).length&&(this.styles[t]||(this.styles[t]={}),this.styles[t][e+s]=m({},r[s]));return}if(!i)return;const o=i[e?e-1:1];for(;o&&s--;)this.styles[t][e+s]=m({},o)}insertNewStyleBlock(t,e,s){const r=this.get2DCursorLocation(e,!0),i=[0];let n,o=0;for(let h=0;h<t.length;h++)t[h]===`
`?(o++,i[o]=0):i[o]++;for(i[0]>0&&(this.insertCharStyleObject(r.lineIndex,r.charIndex,i[0],s),s=s&&s.slice(i[0]+1)),o&&this.insertNewlineStyleObject(r.lineIndex,r.charIndex+i[0],o),n=1;n<o;n++)i[n]>0?this.insertCharStyleObject(r.lineIndex+n,0,i[n],s):s&&this.styles[r.lineIndex+n]&&s[0]&&(this.styles[r.lineIndex+n][0]=s[0]),s=s&&s.slice(i[n]+1);i[n]>0&&this.insertCharStyleObject(r.lineIndex+n,0,i[n],s)}removeChars(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t+1;this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}insertChars(t,e,s){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:s;r>s&&this.removeStyleFromTo(s,r);const i=this.graphemeSplit(t);this.insertNewStyleBlock(i,s,e),this._text=[...this._text.slice(0,s),...i,...this._text.slice(r)],this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}setSelectionStartEndWithShift(t,e,s){s<=t?(e===t?this._selectionDirection=j:this._selectionDirection===z&&(this._selectionDirection=j,this.selectionEnd=t),this.selectionStart=s):s>t&&s<e?this._selectionDirection===z?this.selectionEnd=s:this.selectionStart=s:(e===t?this._selectionDirection=z:this._selectionDirection===j&&(this._selectionDirection=z,this.selectionStart=e),this.selectionEnd=s)}}class eh extends th{initHiddenTextarea(){const t=this.canvas&&_t(this.canvas.getElement())||me(),e=t.createElement("textarea");Object.entries({autocapitalize:"off",autocorrect:"off",autocomplete:"off",spellcheck:"false","data-fabric":"textarea",wrap:"off"}).map(n=>{let[o,h]=n;return e.setAttribute(o,h)});const{top:s,left:r,fontSize:i}=this._calcTextareaPosition();e.style.cssText="position: absolute; top: ".concat(s,"; left: ").concat(r,"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; padding-top: ").concat(i,";"),(this.hiddenTextareaContainer||t.body).appendChild(e),Object.entries({blur:"blur",keydown:"onKeyDown",keyup:"onKeyUp",input:"onInput",copy:"copy",cut:"copy",paste:"paste",compositionstart:"onCompositionStart",compositionupdate:"onCompositionUpdate",compositionend:"onCompositionEnd"}).map(n=>{let[o,h]=n;return e.addEventListener(o,this[h].bind(this))}),this.hiddenTextarea=e}blur(){this.abortCursorAnimation()}onKeyDown(t){if(!this.isEditing)return;const e=this.direction==="rtl"?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown)||!t.ctrlKey&&!t.metaKey)return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}onKeyUp(t){!this.isEditing||this._copyDone||this.inCompositionMode?this._copyDone=!1:t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll())}onInput(t){const e=this.fromPaste,{value:s,selectionStart:r,selectionEnd:i}=this.hiddenTextarea;if(this.fromPaste=!1,t&&t.stopPropagation(),!this.isEditing)return;const n=()=>{this.updateFromTextArea(),this.fire(Cs),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())};if(this.hiddenTextarea.value==="")return this.styles={},void n();const o=this._splitTextIntoLines(s).graphemeText,h=this._text.length,l=o.length,c=this.selectionStart,u=this.selectionEnd,g=c!==u;let d,f,v,y,x=l-h;const C=this.fromStringToGraphemeSelection(r,i,s),w=c>C.selectionStart;g?(f=this._text.slice(c,u),x+=u-c):l<h&&(f=w?this._text.slice(u+x,u):this._text.slice(c,c-x));const S=o.slice(C.selectionEnd-x,C.selectionEnd);if(f&&f.length&&(S.length&&(d=this.getSelectionStyles(c,c+1,!1),d=S.map(()=>d[0])),g?(v=c,y=u):w?(v=u-f.length,y=u):(v=u,y=u+f.length),this.removeStyleFromTo(v,y)),S.length){const{copyPasteData:b}=Ot();e&&S.join("")===b.copiedText&&!E.disableStyleCopyPaste&&(d=b.copiedTextStyle),this.insertNewStyleBlock(S,c,d)}n()}onCompositionStart(){this.inCompositionMode=!0}onCompositionEnd(){this.inCompositionMode=!1}onCompositionUpdate(t){let{target:e}=t;const{selectionStart:s,selectionEnd:r}=e;this.compositionStart=s,this.compositionEnd=r,this.updateTextareaPosition()}copy(){if(this.selectionStart===this.selectionEnd)return;const{copyPasteData:t}=Ot();t.copiedText=this.getSelectedText(),E.disableStyleCopyPaste?t.copiedTextStyle=void 0:t.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0}paste(){this.fromPaste=!0}_getWidthBeforeCursor(t,e){let s,r=this._getLineLeftOffset(t);return e>0&&(s=this.__charBounds[t][e-1],r+=s.left+s.width),r}getDownCursorOffset(t,e){const s=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(s),i=r.lineIndex;if(i===this._textLines.length-1||t.metaKey||t.keyCode===34)return this._text.length-s;const n=r.charIndex,o=this._getWidthBeforeCursor(i,n),h=this._getIndexOnLine(i+1,o);return this._textLines[i].slice(n).length+h+1+this.missingNewlineOffset(i)}_getSelectionForOffset(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart}getUpCursorOffset(t,e){const s=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(s),i=r.lineIndex;if(i===0||t.metaKey||t.keyCode===33)return-s;const n=r.charIndex,o=this._getWidthBeforeCursor(i,n),h=this._getIndexOnLine(i-1,o),l=this._textLines[i].slice(0,n),c=this.missingNewlineOffset(i-1);return-this._textLines[i-1].length+h-l.length+(1-c)}_getIndexOnLine(t,e){const s=this._textLines[t];let r,i,n=this._getLineLeftOffset(t),o=0;for(let h=0,l=s.length;h<l;h++)if(r=this.__charBounds[t][h].width,n+=r,n>e){i=!0;const c=n-r,u=n,g=Math.abs(c-e);o=Math.abs(u-e)<g?h:h-1;break}return i||(o=s.length-1),o}moveCursorDown(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)}moveCursorUp(t){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorUpOrDown("Up",t)}_moveCursorUpOrDown(t,e){const s=this["get".concat(t,"CursorOffset")](e,this._selectionDirection===z);if(e.shiftKey?this.moveCursorWithShift(s):this.moveCursorWithoutShift(s),s!==0){const r=this.text.length;this.selectionStart=fe(0,this.selectionStart,r),this.selectionEnd=fe(0,this.selectionEnd,r),this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea()}}moveCursorWithShift(t){const e=this._selectionDirection===j?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),t!==0}moveCursorWithoutShift(t){return t<0?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),t!==0}moveCursorLeft(t){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorLeftOrRight("Left",t)}_move(t,e,s){let r;if(t.altKey)r=this["findWordBoundary".concat(s)](this[e]);else{if(!t.metaKey&&t.keyCode!==35&&t.keyCode!==36)return this[e]+=s==="Left"?-1:1,!0;r=this["findLineBoundary".concat(s)](this[e])}return r!==void 0&&this[e]!==r&&(this[e]=r,!0)}_moveLeft(t,e){return this._move(t,e,"Left")}_moveRight(t,e){return this._move(t,e,"Right")}moveCursorLeftWithoutShift(t){let e=!0;return this._selectionDirection=j,this.selectionEnd===this.selectionStart&&this.selectionStart!==0&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e}moveCursorLeftWithShift(t){return this._selectionDirection===z&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):this.selectionStart!==0?(this._selectionDirection=j,this._moveLeft(t,"selectionStart")):void 0}moveCursorRight(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)}_moveCursorLeftOrRight(t,e){const s="moveCursor".concat(t).concat(e.shiftKey?"WithShift":"WithoutShift");this._currentCursorOpacity=1,this[s](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())}moveCursorRightWithShift(t){return this._selectionDirection===j&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection=z,this._moveRight(t,"selectionEnd")):void 0}moveCursorRightWithoutShift(t){let e=!0;return this._selectionDirection=z,this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e}}const yi=a=>!!a.button;class sh extends eh{constructor(){super(...arguments),p(this,"draggableTextDelegate",void 0)}initBehavior(){this.on("mousedown",this._mouseDownHandler),this.on("mouseup",this.mouseUpHandler),this.on("mousedblclick",this.doubleClickHandler),this.on("mousetripleclick",this.tripleClickHandler),this.draggableTextDelegate=new Qa(this),super.initBehavior()}shouldStartDragging(){return this.draggableTextDelegate.isActive()}onDragStart(t){return this.draggableTextDelegate.onDragStart(t)}canDrop(t){return this.draggableTextDelegate.canDrop(t)}doubleClickHandler(t){this.isEditing&&(this.selectWord(this.getSelectionStartFromPointer(t.e)),this.renderCursorOrSelection())}tripleClickHandler(t){this.isEditing&&(this.selectLine(this.getSelectionStartFromPointer(t.e)),this.renderCursorOrSelection())}_mouseDownHandler(t){let{e,alreadySelected:s}=t;this.canvas&&this.editable&&!yi(e)&&!this.getActiveControl()&&(this.draggableTextDelegate.start(e)||(this.canvas.textEditingManager.register(this),s&&(this.inCompositionMode=!1,this.setCursorByClick(e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()),this.selected||(this.selected=s||this.isEditing)))}mouseUpHandler(t){let{e,transform:s}=t;const r=this.draggableTextDelegate.end(e);if(this.canvas){this.canvas.textEditingManager.unregister(this);const i=this.canvas._activeObject;if(i&&i!==this)return}!this.editable||this.group&&!this.group.interactive||s&&s.actionPerformed||yi(e)||r||this.selected&&!this.getActiveControl()&&(this.enterEditing(e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection())}setCursorByClick(t){const e=this.getSelectionStartFromPointer(t),s=this.selectionStart,r=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(s,r,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())}getSelectionStartFromPointer(t){const e=this.canvas.getScenePoint(t).transform(xt(this.calcTransformMatrix())).add(new _(-this._getLeftOffset(),-this._getTopOffset()));let s=0,r=0,i=0;for(let l=0;l<this._textLines.length&&s<=e.y;l++)s+=this.getHeightOfLine(l),i=l,l>0&&(r+=this._textLines[l-1].length+this.missingNewlineOffset(l-1));let n=Math.abs(this._getLineLeftOffset(i));const o=this._textLines[i].length,h=this.__charBounds[i];for(let l=0;l<o;l++){const c=n+h[l].kernedWidth;if(e.x<=c){Math.abs(e.x-c)<=Math.abs(e.x-n)&&r++;break}n=c,r++}return Math.min(this.flipX?o-r:r,this._text.length)}}const ls="moveCursorUp",cs="moveCursorDown",us="moveCursorLeft",gs="moveCursorRight",ds="exitEditing",_i=(a,t)=>{const e=t.getRetinaScaling();a.setTransform(e,0,0,e,0,0);const s=t.viewportTransform;a.transform(s[0],s[1],s[2],s[3],s[4],s[5])},rh=m({selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,keysMap:{9:ds,27:ds,33:ls,34:cs,35:gs,36:us,37:us,38:ls,39:gs,40:cs},keysMapRtl:{9:ds,27:ds,33:ls,34:cs,35:us,36:gs,37:gs,38:ls,39:us,40:cs},ctrlKeysMapDown:{65:"cmdAll"},ctrlKeysMapUp:{67:"copy",88:"cut"}},{_selectionDirection:null,_reSpace:/\s|\r?\n/,inCompositionMode:!1});class Pt extends sh{static getDefaults(){return m(m({},super.getDefaults()),Pt.ownDefaults)}get type(){const t=super.type;return t==="itext"?"i-text":t}constructor(t,e){super(t,m(m({},Pt.ownDefaults),e)),this.initBehavior()}_set(t,e){return this.isEditing&&this._savedProps&&t in this._savedProps?(this._savedProps[t]=e,this):(t==="canvas"&&(this.canvas instanceof mr&&this.canvas.textEditingManager.remove(this),e instanceof mr&&e.textEditingManager.add(this)),super._set(t,e))}setSelectionStart(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)}setSelectionEnd(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)}_updateAndFire(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()}_fireSelectionChanged(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})}initDimensions(){this.isEditing&&this.initDelayedCursor(),super.initDimensions()}getSelectionStyles(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart||0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.selectionEnd,s=arguments.length>2?arguments[2]:void 0;return super.getSelectionStyles(t,e,s)}setSelectionStyles(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.selectionStart||0,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.selectionEnd;return super.setSelectionStyles(t,e,s)}get2DCursorLocation(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;return super.get2DCursorLocation(t,e)}render(t){super.render(t),this.cursorOffsetCache={},this.renderCursorOrSelection()}toCanvasElement(t){const e=this.isEditing;this.isEditing=!1;const s=super.toCanvasElement(t);return this.isEditing=e,s}renderCursorOrSelection(){if(!this.isEditing||!this.canvas)return;const t=this.clearContextTop(!0);if(!t)return;const e=this._getCursorBoundaries(),s=this.findAncestorsWithClipPath(),r=s.length>0;let i,n=t;if(r){i=dt(t.canvas),n=i.getContext("2d"),_i(n,this.canvas);const o=this.calcTransformMatrix();n.transform(o[0],o[1],o[2],o[3],o[4],o[5])}if(this.selectionStart!==this.selectionEnd||this.inCompositionMode?this.renderSelection(n,e):this.renderCursor(n,e),r)for(const o of s){const h=o.clipPath,l=dt(t.canvas),c=l.getContext("2d");if(_i(c,this.canvas),!h.absolutePositioned){const u=o.calcTransformMatrix();c.transform(u[0],u[1],u[2],u[3],u[4],u[5])}h.transform(c),h.drawObject(c,!0,{}),this.drawClipPathOnCache(n,h,l)}r&&(t.setTransform(1,0,0,1,0,0),t.drawImage(i,0,0)),this.canvas.contextTopDirty=!0,t.restore()}findAncestorsWithClipPath(){const t=[];let e=this;for(;e;)e.clipPath&&t.push(e),e=e.parent;return t}_getCursorBoundaries(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;const s=this._getLeftOffset(),r=this._getTopOffset(),i=this._getCursorBoundariesOffsets(t,e);return{left:s,top:r,leftOffset:i.left,topOffset:i.top}}_getCursorBoundariesOffsets(t,e){return e?this.__getCursorBoundariesOffsets(t):this.cursorOffsetCache&&"top"in this.cursorOffsetCache?this.cursorOffsetCache:this.cursorOffsetCache=this.__getCursorBoundariesOffsets(t)}__getCursorBoundariesOffsets(t){let e=0,s=0;const{charIndex:r,lineIndex:i}=this.get2DCursorLocation(t);for(let l=0;l<i;l++)e+=this.getHeightOfLine(l);const n=this._getLineLeftOffset(i),o=this.__charBounds[i][r];o&&(s=o.left),this.charSpacing!==0&&r===this._textLines[i].length&&(s-=this._getWidthOfCharSpacing());const h={top:e,left:n+(s>0?s:0)};return this.direction==="rtl"&&(this.textAlign===z||this.textAlign===wt||this.textAlign===Ge?h.left*=-1:this.textAlign===j||this.textAlign===Es?h.left=n-(s>0?s:0):this.textAlign!==k&&this.textAlign!==Ne||(h.left=n-(s>0?s:0))),h}renderCursorAt(t){this._renderCursor(this.canvas.contextTop,this._getCursorBoundaries(t,!0),t)}renderCursor(t,e){this._renderCursor(t,e,this.selectionStart)}getCursorRenderingData(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._getCursorBoundaries(t);const s=this.get2DCursorLocation(t),r=s.lineIndex,i=s.charIndex>0?s.charIndex-1:0,n=this.getValueOfPropertyAt(r,i,"fontSize"),o=this.getObjectScaling().x*this.canvas.getZoom(),h=this.cursorWidth/o,l=this.getValueOfPropertyAt(r,i,"deltaY"),c=e.topOffset+(1-this._fontSizeFraction)*this.getHeightOfLine(r)/this.lineHeight-n*(1-this._fontSizeFraction);return{color:this.cursorColor||this.getValueOfPropertyAt(r,i,"fill"),opacity:this._currentCursorOpacity,left:e.left+e.leftOffset-h/2,top:c+e.top+l,width:h,height:n}}_renderCursor(t,e,s){const{color:r,opacity:i,left:n,top:o,width:h,height:l}=this.getCursorRenderingData(s,e);t.fillStyle=r,t.globalAlpha=i,t.fillRect(n,o,h,l)}renderSelection(t,e){const s={selectionStart:this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,selectionEnd:this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd};this._renderSelection(t,s,e)}renderDragSourceEffect(){const t=this.draggableTextDelegate.getDragStartSelection();this._renderSelection(this.canvas.contextTop,t,this._getCursorBoundaries(t.selectionStart,!0))}renderDropTargetEffect(t){const e=this.getSelectionStartFromPointer(t);this.renderCursorAt(e)}_renderSelection(t,e,s){const r=e.selectionStart,i=e.selectionEnd,n=this.textAlign.includes(wt),o=this.get2DCursorLocation(r),h=this.get2DCursorLocation(i),l=o.lineIndex,c=h.lineIndex,u=o.charIndex<0?0:o.charIndex,g=h.charIndex<0?0:h.charIndex;for(let d=l;d<=c;d++){const f=this._getLineLeftOffset(d)||0;let v=this.getHeightOfLine(d),y=0,x=0,C=0;if(d===l&&(x=this.__charBounds[l][u].left),d>=l&&d<c)C=n&&!this.isEndOfWrapping(d)?this.width:this.getLineWidth(d)||5;else if(d===c)if(g===0)C=this.__charBounds[c][g].left;else{const M=this._getWidthOfCharSpacing();C=this.__charBounds[c][g-1].left+this.__charBounds[c][g-1].width-M}y=v,(this.lineHeight<1||d===c&&this.lineHeight>1)&&(v/=this.lineHeight);let w=s.left+f+x,S=v,b=0;const D=C-x;this.inCompositionMode?(t.fillStyle=this.compositionColor||"black",S=1,b=v):t.fillStyle=this.selectionColor,this.direction==="rtl"&&(this.textAlign===z||this.textAlign===wt||this.textAlign===Ge?w=this.width-w-D:this.textAlign===j||this.textAlign===Es?w=s.left+f-C:this.textAlign!==k&&this.textAlign!==Ne||(w=s.left+f-C)),t.fillRect(w,s.top+s.topOffset+b,D,S),s.topOffset+=y}}getCurrentCharFontSize(){const t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")}getCurrentCharColor(){const t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,K)}_getCurrentCharIndex(){const t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}dispose(){this.exitEditingImpl(),this.draggableTextDelegate.dispose(),super.dispose()}}p(Pt,"ownDefaults",rh),p(Pt,"type","IText"),T.setClass(Pt),T.setClass(Pt,"i-text");class te extends Pt{static getDefaults(){return m(m({},super.getDefaults()),te.ownDefaults)}constructor(t,e){super(t,m(m({},te.ownDefaults),e))}static createControls(){return{controls:Po()}}initDimensions(){this.initialized&&(this.isEditing&&this.initDelayedCursor(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.includes(wt)&&this.enlargeSpaces(),this.height=this.calcTextHeight())}_generateStyleMap(t){let e=0,s=0,r=0;const i={};for(let n=0;n<t.graphemeLines.length;n++)t.graphemeText[r]===`
`&&n>0?(s=0,r++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[r])&&n>0&&(s++,r++),i[n]={line:e,offset:s},r+=t.graphemeLines[n].length,s+=t.graphemeLines[n].length;return i}styleHas(t,e){if(this._styleMap&&!this.isWrapping){const s=this._styleMap[e];s&&(e=s.line)}return super.styleHas(t,e)}isEmptyStyles(t){if(!this.styles)return!0;let e,s=0,r=t+1,i=!1;const n=this._styleMap[t],o=this._styleMap[t+1];n&&(t=n.line,s=n.offset),o&&(r=o.line,i=r===t,e=o.offset);const h=t===void 0?this.styles:{line:this.styles[t]};for(const l in h)for(const c in h[l]){const u=parseInt(c,10);if(u>=s&&(!i||u<e))for(const g in h[l][c])return!1}return!0}_getStyleDeclaration(t,e){if(this._styleMap&&!this.isWrapping){const s=this._styleMap[t];if(!s)return{};t=s.line,e=s.offset+e}return super._getStyleDeclaration(t,e)}_setStyleDeclaration(t,e,s){const r=this._styleMap[t];super._setStyleDeclaration(r.line,r.offset+e,s)}_deleteStyleDeclaration(t,e){const s=this._styleMap[t];super._deleteStyleDeclaration(s.line,s.offset+e)}_getLineStyle(t){const e=this._styleMap[t];return!!this.styles[e.line]}_setLineStyle(t){const e=this._styleMap[t];super._setLineStyle(e.line)}_wrapText(t,e){this.isWrapping=!0;const s=this.getGraphemeDataForRender(t),r=[];for(let i=0;i<s.wordsData.length;i++)r.push(...this._wrapLine(i,e,s));return this.isWrapping=!1,r}getGraphemeDataForRender(t){const e=this.splitByGrapheme,s=e?"":" ";let r=0;return{wordsData:t.map((i,n)=>{let o=0;const h=e?this.graphemeSplit(i):this.wordSplit(i);return h.length===0?[{word:[],width:0}]:h.map(l=>{const c=e?[l]:this.graphemeSplit(l),u=this._measureWord(c,n,o);return r=Math.max(u,r),o+=c.length+s.length,{word:c,width:u}})}),largestWordWidth:r}}_measureWord(t,e){let s,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=0;for(let n=0,o=t.length;n<o;n++)i+=this._getGraphemeBox(t[n],e,n+r,s,!0).kernedWidth,s=t[n];return i}wordSplit(t){return t.split(this._wordJoiners)}_wrapLine(t,e,s){let{largestWordWidth:r,wordsData:i}=s,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;const o=this._getWidthOfCharSpacing(),h=this.splitByGrapheme,l=[],c=h?"":" ";let u=0,g=[],d=0,f=0,v=!0;e-=n;const y=Math.max(e,r,this.dynamicMinWidth),x=i[t];let C;for(d=0,C=0;C<x.length;C++){const{word:w,width:S}=x[C];d+=w.length,u+=f+S-o,u>y&&!v?(l.push(g),g=[],u=S,v=!0):u+=o,v||h||g.push(c),g=g.concat(w),f=h?0:this._measureWord([c],t,d),d++,v=!1}return C&&l.push(g),r+n>this.dynamicMinWidth&&(this.dynamicMinWidth=r-o+n),l}isEndOfWrapping(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line}missingNewlineOffset(t,e){return this.splitByGrapheme&&!e?this.isEndOfWrapping(t)?1:0:1}_splitTextIntoLines(t){const e=super._splitTextIntoLines(t),s=this._wrapText(e.lines,this.width),r=new Array(s.length);for(let i=0;i<s.length;i++)r[i]=s[i].join("");return e.lines=r,e.graphemeLines=s,e}getMinWidth(){return Math.max(this.minWidth,this.dynamicMinWidth)}_removeExtraneousStyles(){const t=new Map;for(const e in this._styleMap){const s=parseInt(e,10);if(this._textLines[s]){const r=this._styleMap[e].line;t.set("".concat(r),!0)}}for(const e in this.styles)t.has(e)||delete this.styles[e]}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject(["minWidth","splitByGrapheme",...t])}}p(te,"type","Textbox"),p(te,"textLayoutProperties",[...Pt.textLayoutProperties,"width"]),p(te,"ownDefaults",{minWidth:20,dynamicMinWidth:2,lockScalingFlip:!0,noScaleCache:!1,_wordJoiners:/[ \t\r]/,splitByGrapheme:!1}),T.setClass(te);class xi extends Xs{shouldPerformLayout(t){return!!t.target.clipPath&&super.shouldPerformLayout(t)}shouldLayoutClipPath(){return!1}calcLayoutResult(t,e){const{target:s}=t,{clipPath:r,group:i}=s;if(!r||!this.shouldPerformLayout(t))return;const{width:n,height:o}=At(en(s,r)),h=new _(n,o);if(r.absolutePositioned)return{center:ue(r.getRelativeCenterPoint(),void 0,i?i.calcTransformMatrix():void 0),size:h};{const l=r.getRelativeCenterPoint().transform(s.calcOwnMatrix(),!0);if(this.shouldPerformLayout(t)){const{center:c=new _,correction:u=new _}=this.calcBoundingBox(e,t)||{};return{center:c.add(l),correction:u.subtract(l),size:h}}return{center:s.getRelativeCenterPoint().add(l),size:h}}}}p(xi,"type","clip-path"),T.setClass(xi);class Ci extends Xs{getInitialSize(t,e){let{target:s}=t,{size:r}=e;return new _(s.width||r.x,s.height||r.y)}}p(Ci,"type","fixed"),T.setClass(Ci);class ih extends $e{subscribeTargets(t){const e=t.target;t.targets.reduce((s,r)=>(r.parent&&s.add(r.parent),s),new Set).forEach(s=>{s.layoutManager.subscribeTargets({target:s,targets:[e]})})}unsubscribeTargets(t){const e=t.target,s=e.getObjects();t.targets.reduce((r,i)=>(i.parent&&r.add(i.parent),r),new Set).forEach(r=>{!s.some(i=>i.parent===r)&&r.layoutManager.unsubscribeTargets({target:r,targets:[e]})})}}class ee extends re{static getDefaults(){return m(m({},super.getDefaults()),ee.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,ee.ownDefaults),this.setOptions(e);const{left:s,top:r,layoutManager:i}=e;this.groupInit(t,{left:s,top:r,layoutManager:i??new ih})}_shouldSetNestedCoords(){return!0}__objectSelectionMonitor(){}multiSelectAdd(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];this.multiSelectionStacking==="selection-order"?this.add(...e):e.forEach(r=>{const i=this._objects.findIndex(o=>o.isInFrontOf(r)),n=i===-1?this.size():i;this.insertAt(n,r)})}canEnterGroup(t){return this.getObjects().some(e=>e.isDescendantOf(t)||t.isDescendantOf(e))?(Gt("error","ActiveSelection: circular object trees are not supported, this call has no effect"),!1):super.canEnterGroup(t)}enterGroup(t,e){t.parent&&t.parent===t.group?t.parent._exitGroup(t):t.group&&t.parent!==t.group&&t.group.remove(t),this._enterGroup(t,e)}exitGroup(t,e){this._exitGroup(t,e),t.parent&&t.parent._enterGroup(t,!0)}_onAfterObjectsChange(t,e){super._onAfterObjectsChange(t,e);const s=new Set;e.forEach(r=>{const{parent:i}=r;i&&s.add(i)}),t===Rr?s.forEach(r=>{r._onAfterObjectsChange(Ds,e)}):s.forEach(r=>{r._set("dirty",!0)})}onDeselect(){return this.removeAll(),!1}toString(){return"#<ActiveSelection: (".concat(this.complexity(),")>")}shouldCache(){return!1}isOnACache(){return!1}_renderControls(t,e,s){t.save(),t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1;const r=m(m({hasControls:!1},s),{},{forActiveSelection:!0});for(let i=0;i<this._objects.length;i++)this._objects[i]._renderControls(t,r);super._renderControls(t,e),t.restore()}}p(ee,"type","ActiveSelection"),p(ee,"ownDefaults",{multiSelectionStacking:"canvas-stacking"}),T.setClass(ee),T.setClass(ee,"activeSelection");class nh{constructor(){p(this,"resources",{})}applyFilters(t,e,s,r,i){const n=i.getContext("2d");if(!n)return;n.drawImage(e,0,0,s,r);const o={sourceWidth:s,sourceHeight:r,imageData:n.getImageData(0,0,s,r),originalEl:e,originalImageData:n.getImageData(0,0,s,r),canvasEl:i,ctx:n,filterBackend:this};t.forEach(l=>{l.applyTo(o)});const{imageData:h}=o;return h.width===s&&h.height===r||(i.width=h.width,i.height=h.height),n.putImageData(h,0,0),o}}class bn{constructor(){let{tileSize:t=E.textureSize}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};p(this,"aPosition",new Float32Array([0,0,0,1,1,0,1,1])),p(this,"resources",{}),this.tileSize=t,this.setupGLContext(t,t),this.captureGPUInfo()}setupGLContext(t,e){this.dispose(),this.createWebGLCanvas(t,e)}createWebGLCanvas(t,e){const s=dt({width:t,height:e}),r=s.getContext("webgl",{alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1});r&&(r.clearColor(0,0,0,0),this.canvas=s,this.gl=r)}applyFilters(t,e,s,r,i,n){const o=this.gl,h=i.getContext("2d");if(!o||!h)return;let l;n&&(l=this.getCachedTexture(n,e));const c={originalWidth:e.width||e.naturalWidth||0,originalHeight:e.height||e.naturalHeight||0,sourceWidth:s,sourceHeight:r,destinationWidth:s,destinationHeight:r,context:o,sourceTexture:this.createTexture(o,s,r,l?void 0:e),targetTexture:this.createTexture(o,s,r),originalTexture:l||this.createTexture(o,s,r,l?void 0:e),passes:t.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:i},u=o.createFramebuffer();return o.bindFramebuffer(o.FRAMEBUFFER,u),t.forEach(g=>{g&&g.applyTo(c)}),function(g){const d=g.targetCanvas,f=d.width,v=d.height,y=g.destinationWidth,x=g.destinationHeight;f===y&&v===x||(d.width=y,d.height=x)}(c),this.copyGLTo2D(o,c),o.bindTexture(o.TEXTURE_2D,null),o.deleteTexture(c.sourceTexture),o.deleteTexture(c.targetTexture),o.deleteFramebuffer(u),h.setTransform(1,0,0,1,0,0),c}dispose(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()}clearWebGLCaches(){this.programCache={},this.textureCache={}}createTexture(t,e,s,r,i){const{NEAREST:n,TEXTURE_2D:o,RGBA:h,UNSIGNED_BYTE:l,CLAMP_TO_EDGE:c,TEXTURE_MAG_FILTER:u,TEXTURE_MIN_FILTER:g,TEXTURE_WRAP_S:d,TEXTURE_WRAP_T:f}=t,v=t.createTexture();return t.bindTexture(o,v),t.texParameteri(o,u,i||n),t.texParameteri(o,g,i||n),t.texParameteri(o,d,c),t.texParameteri(o,f,c),r?t.texImage2D(o,0,h,h,l,r):t.texImage2D(o,0,h,e,s,0,h,l,null),v}getCachedTexture(t,e,s){const{textureCache:r}=this;if(r[t])return r[t];{const i=this.createTexture(this.gl,e.width,e.height,e,s);return i&&(r[t]=i),i}}evictCachesForKey(t){this.textureCache[t]&&(this.gl.deleteTexture(this.textureCache[t]),delete this.textureCache[t])}copyGLTo2D(t,e){const s=t.canvas,r=e.targetCanvas,i=r.getContext("2d");if(!i)return;i.translate(0,r.height),i.scale(1,-1);const n=s.height-r.height;i.drawImage(s,0,n,r.width,r.height,0,0,r.width,r.height)}copyGLTo2DPutImageData(t,e){const s=e.targetCanvas.getContext("2d"),r=e.destinationWidth,i=e.destinationHeight,n=r*i*4;if(!s)return;const o=new Uint8Array(this.imageBuffer,0,n),h=new Uint8ClampedArray(this.imageBuffer,0,n);t.readPixels(0,0,r,i,t.RGBA,t.UNSIGNED_BYTE,o);const l=new ImageData(h,r,i);s.putImageData(l,0,0)}captureGPUInfo(){if(this.gpuInfo)return this.gpuInfo;const t=this.gl,e={renderer:"",vendor:""};if(!t)return e;const s=t.getExtension("WEBGL_debug_renderer_info");if(s){const r=t.getParameter(s.UNMASKED_RENDERER_WEBGL),i=t.getParameter(s.UNMASKED_VENDOR_WEBGL);r&&(e.renderer=r.toLowerCase()),i&&(e.vendor=i.toLowerCase())}return this.gpuInfo=e,e}}let Qs;function oh(){const{WebGLProbe:a}=Ot();return a.queryWebGL(Bt()),E.enableGLFiltering&&a.isSupported(E.textureSize)?new bn({tileSize:E.textureSize}):new nh}function tr(){return!Qs&&(!(arguments.length>0&&arguments[0]!==void 0)||arguments[0])&&(Qs=oh()),Qs}const ah=["filters","resizeFilter","src","crossOrigin","type"],Sn=["cropX","cropY"];class lt extends Z{static getDefaults(){return m(m({},super.getDefaults()),lt.ownDefaults)}constructor(t,e){super(),p(this,"_lastScaleX",1),p(this,"_lastScaleY",1),p(this,"_filterScalingX",1),p(this,"_filterScalingY",1),this.filters=[],Object.assign(this,lt.ownDefaults),this.setOptions(e),this.cacheKey="texture".concat(Nt()),this.setElement(typeof t=="string"?(this.canvas&&_t(this.canvas.getElement())||me()).getElementById(t):t,e)}getElement(){return this._element}setElement(t){var e;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._element=t,this._originalElement=t,this._setWidthHeight(s),(e=t.classList)===null||e===void 0||e.add(lt.CSS_CANVAS),this.filters.length!==0&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters()}removeTexture(t){const e=tr(!1);e instanceof bn&&e.evictCachesForKey(t)}dispose(){super.dispose(),this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._cacheContext=null,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(t=>{const e=this[t];e&&Ot().dispose(e),this[t]=void 0})}getCrossOrigin(){return this._originalElement&&(this._originalElement.crossOrigin||null)}getOriginalSize(){const t=this.getElement();return t?{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}:{width:0,height:0}}_stroke(t){if(!this.stroke||this.strokeWidth===0)return;const e=this.width/2,s=this.height/2;t.beginPath(),t.moveTo(-e,-s),t.lineTo(e,-s),t.lineTo(e,s),t.lineTo(-e,s),t.lineTo(-e,-s),t.closePath()}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=[];return this.filters.forEach(s=>{s&&e.push(s.toObject())}),m(m({},super.toObject([...Sn,...t])),{},{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:e},this.resizeFilter?{resizeFilter:this.resizeFilter.toObject()}:{})}hasCrop(){return!!this.cropX||!!this.cropY||this.width<this._element.width||this.height<this._element.height}_toSVG(){const t=[],e=this._element,s=-this.width/2,r=-this.height/2;let i=[],n=[],o="",h="";if(!e)return[];if(this.hasCrop()){const l=Nt();i.push('<clipPath id="imageCrop_'+l+`">
`,'	<rect x="'+s+'" y="'+r+'" width="'+this.width+'" height="'+this.height+`" />
`,`</clipPath>
`),o=' clip-path="url(#imageCrop_'+l+')" '}if(this.imageSmoothing||(h=' image-rendering="optimizeSpeed"'),t.push("	<image ","COMMON_PARTS",'xlink:href="'.concat(this.getSvgSrc(!0),'" x="').concat(s-this.cropX,'" y="').concat(r-this.cropY,'" width="').concat(e.width||e.naturalWidth,'" height="').concat(e.height||e.naturalHeight,'"').concat(h).concat(o,`></image>
`)),this.stroke||this.strokeDashArray){const l=this.fill;this.fill=null,n=['	<rect x="'.concat(s,'" y="').concat(r,'" width="').concat(this.width,'" height="').concat(this.height,'" style="').concat(this.getSvgStyles(),`" />
`)],this.fill=l}return i=this.paintFirst!==K?i.concat(n,t):i.concat(t,n),i}getSrc(t){const e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src")||"":e.src:this.src||""}getSvgSrc(t){return this.getSrc(t)}setSrc(t){let{crossOrigin:e,signal:s}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return vs(t,{crossOrigin:e,signal:s}).then(r=>{e!==void 0&&this.set({crossOrigin:e}),this.setElement(r)})}toString(){return'#<Image: { src: "'.concat(this.getSrc(),'" }>')}applyResizeFilters(){const t=this.resizeFilter,e=this.minimumScaleTrigger,s=this.getTotalObjectScaling(),r=s.x,i=s.y,n=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||r>e&&i>e)return this._element=n,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=r,void(this._lastScaleY=i);const o=dt(n),{width:h,height:l}=n;this._element=o,this._lastScaleX=t.scaleX=r,this._lastScaleY=t.scaleY=i,tr().applyFilters([t],n,h,l,this._element),this._filterScalingX=o.width/this._originalElement.width,this._filterScalingY=o.height/this._originalElement.height}applyFilters(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.filters||[];if(t=t.filter(i=>i&&!i.isNeutralState()),this.set("dirty",!0),this.removeTexture("".concat(this.cacheKey,"_filtered")),t.length===0)return this._element=this._originalElement,this._filteredEl=void 0,this._filterScalingX=1,void(this._filterScalingY=1);const e=this._originalElement,s=e.naturalWidth||e.width,r=e.naturalHeight||e.height;if(this._element===this._originalElement){const i=dt({width:s,height:r});this._element=i,this._filteredEl=i}else this._filteredEl&&(this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,s,r),this._lastScaleX=1,this._lastScaleY=1);tr().applyFilters(t,this._originalElement,s,r,this._element,this.cacheKey),this._originalElement.width===this._element.width&&this._originalElement.height===this._element.height||(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height)}_render(t){t.imageSmoothingEnabled=this.imageSmoothing,this.isMoving!==!0&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)}drawCacheOnCanvas(t){t.imageSmoothingEnabled=this.imageSmoothing,super.drawCacheOnCanvas(t)}shouldCache(){return this.needsItsOwnCache()}_renderFill(t){const e=this._element;if(!e)return;const s=this._filterScalingX,r=this._filterScalingY,i=this.width,n=this.height,o=Math.max(this.cropX,0),h=Math.max(this.cropY,0),l=e.naturalWidth||e.width,c=e.naturalHeight||e.height,u=o*s,g=h*r,d=Math.min(i*s,l-u),f=Math.min(n*r,c-g),v=-i/2,y=-n/2,x=Math.min(i,l/s-o),C=Math.min(n,c/r-h);e&&t.drawImage(e,u,g,d,f,v,y,x,C)}_needsResize(){const t=this.getTotalObjectScaling();return t.x!==this._lastScaleX||t.y!==this._lastScaleY}_resetWidthHeight(){this.set(this.getOriginalSize())}_setWidthHeight(){let{width:t,height:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const s=this.getOriginalSize();this.width=t||s.width,this.height=e||s.height}parsePreserveAspectRatioAttribute(){const t=Zn(this.preserveAspectRatio||""),e=this.width,s=this.height,r={width:e,height:s};let i,n=this._element.width,o=this._element.height,h=1,l=1,c=0,u=0,g=0,d=0;return!t||t.alignX===it&&t.alignY===it?(h=e/n,l=s/o):(t.meetOrSlice==="meet"&&(h=l=ga(this._element,r),i=(e-n*h)/2,t.alignX==="Min"&&(c=-i),t.alignX==="Max"&&(c=i),i=(s-o*l)/2,t.alignY==="Min"&&(u=-i),t.alignY==="Max"&&(u=i)),t.meetOrSlice==="slice"&&(h=l=da(this._element,r),i=n-e/h,t.alignX==="Mid"&&(g=i/2),t.alignX==="Max"&&(g=i),i=o-s/l,t.alignY==="Mid"&&(d=i/2),t.alignY==="Max"&&(d=i),n=e/h,o=s/l)),{width:n,height:o,scaleX:h,scaleY:l,offsetLeft:c,offsetTop:u,cropX:g,cropY:d}}static fromObject(t,e){let{filters:s,resizeFilter:r,src:i,crossOrigin:n,type:o}=t,h=I(t,ah);return Promise.all([vs(i,m(m({},e),{},{crossOrigin:n})),s&&qe(s,e),r&&qe([r],e),Ls(h,e)]).then(l=>{let[c,u=[],[g]=[],d={}]=l;return new this(c,m(m({},h),{},{src:i,filters:u,resizeFilter:g},d))})}static fromURL(t){let{crossOrigin:e=null,signal:s}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;return vs(t,{crossOrigin:e,signal:s}).then(i=>new this(i,r))}static async fromElement(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;const r=Wt(t,this.ATTRIBUTE_NAMES,s);return this.fromURL(r["xlink:href"]||r.href,e,r).catch(i=>(Gt("log","Unable to parse Image",i),null))}}p(lt,"type","Image"),p(lt,"cacheProperties",[...Xt,...Sn]),p(lt,"ownDefaults",{strokeWidth:0,srcFromAttribute:!1,minimumScaleTrigger:.5,cropX:0,cropY:0,imageSmoothing:!0}),p(lt,"CSS_CANVAS","canvas-img"),p(lt,"ATTRIBUTE_NAMES",[...qt,"x","y","width","height","preserveAspectRatio","xlink:href","href","crossOrigin","image-rendering"]),T.setClass(lt),T.setSVGClass(lt);Bs(["pattern","defs","symbol","metadata","clipPath","mask","desc"]);const Ys=a=>a.webgl!==void 0,Br="precision highp float",hh=`
    `.concat(Br,`;
    varying vec2 vTexCoord;
    uniform sampler2D uTexture;
    void main() {
      gl_FragColor = texture2D(uTexture, vTexCoord);
    }`),lh=["type"],ch=["type"],uh=new RegExp(Br,"g");class U{get type(){return this.constructor.type}constructor(){let t=I(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},lh);Object.assign(this,this.constructor.defaults,t)}getFragmentSource(){return hh}getVertexSource(){return`
    attribute vec2 aPosition;
    varying vec2 vTexCoord;
    void main() {
      vTexCoord = aPosition;
      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
    }`}createProgram(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getFragmentSource(),s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.getVertexSource();const{WebGLProbe:{GLPrecision:r="highp"}}=Ot();r!=="highp"&&(e=e.replace(uh,Br.replace("highp",r)));const i=t.createShader(t.VERTEX_SHADER),n=t.createShader(t.FRAGMENT_SHADER),o=t.createProgram();if(!i||!n||!o)throw new Tt("Vertex, fragment shader or program creation error");if(t.shaderSource(i,s),t.compileShader(i),!t.getShaderParameter(i,t.COMPILE_STATUS))throw new Tt("Vertex shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(i)));if(t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new Tt("Fragment shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(n)));if(t.attachShader(o,i),t.attachShader(o,n),t.linkProgram(o),!t.getProgramParameter(o,t.LINK_STATUS))throw new Tt('Shader link error for "'.concat(this.type,'" ').concat(t.getProgramInfoLog(o)));const h=this.getUniformLocations(t,o)||{};return h.uStepW=t.getUniformLocation(o,"uStepW"),h.uStepH=t.getUniformLocation(o,"uStepH"),{program:o,attributeLocations:this.getAttributeLocations(t,o),uniformLocations:h}}getAttributeLocations(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}}getUniformLocations(t,e){const s=this.constructor.uniformLocations,r={};for(let i=0;i<s.length;i++)r[s[i]]=t.getUniformLocation(e,s[i]);return r}sendAttributeData(t,e,s){const r=e.aPosition,i=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,i),t.enableVertexAttribArray(r),t.vertexAttribPointer(r,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,s,t.STATIC_DRAW)}_setupFrameBuffer(t){const e=t.context;if(t.passes>1){const s=t.destinationWidth,r=t.destinationHeight;t.sourceWidth===s&&t.sourceHeight===r||(e.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(e,s,r)),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t.targetTexture,0)}else e.bindFramebuffer(e.FRAMEBUFFER,null),e.finish()}_swapTextures(t){t.passes--,t.pass++;const e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e}isNeutralState(t){return!1}applyTo(t){Ys(t)?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){}getCacheKey(){return this.type}retrieveShader(t){const e=this.getCacheKey();return t.programCache[e]||(t.programCache[e]=this.createProgram(t.context)),t.programCache[e]}applyToWebGL(t){const e=t.context,s=this.retrieveShader(t);t.pass===0&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(s.program),this.sendAttributeData(e,s.attributeLocations,t.aPosition),e.uniform1f(s.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(s.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,s.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)}bindAdditionalTexture(t,e,s){t.activeTexture(s),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)}unbindAdditionalTexture(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)}sendUniformData(t,e){}createHelpLayer(t){if(!t.helpLayer){const{sourceWidth:e,sourceHeight:s}=t,r=dt({width:e,height:s});t.helpLayer=r}}toObject(){const t=Object.keys(this.constructor.defaults||{});return m({type:this.type},t.reduce((e,s)=>(e[s]=this[s],e),{}))}toJSON(){return this.toObject()}static async fromObject(t,e){return new this(I(t,ch))}}p(U,"type","BaseFilter"),p(U,"uniformLocations",[]);const gh={multiply:`gl_FragColor.rgb *= uColor.rgb;
`,screen:`gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);
`,add:`gl_FragColor.rgb += uColor.rgb;
`,difference:`gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);
`,subtract:`gl_FragColor.rgb -= uColor.rgb;
`,lighten:`gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);
`,darken:`gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);
`,exclusion:`gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);
`,overlay:`
    if (uColor.r < 0.5) {
      gl_FragColor.r *= 2.0 * uColor.r;
    } else {
      gl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);
    }
    if (uColor.g < 0.5) {
      gl_FragColor.g *= 2.0 * uColor.g;
    } else {
      gl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);
    }
    if (uColor.b < 0.5) {
      gl_FragColor.b *= 2.0 * uColor.b;
    } else {
      gl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);
    }
    `,tint:`
    gl_FragColor.rgb *= (1.0 - uColor.a);
    gl_FragColor.rgb += uColor.rgb;
    `};class ke extends U{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return`
      precision highp float;
      uniform sampler2D uTexture;
      uniform vec4 uColor;
      varying vec2 vTexCoord;
      void main() {
        vec4 color = texture2D(uTexture, vTexCoord);
        gl_FragColor = color;
        if (color.a > 0.0) {
          `.concat(gh[this.mode],`
        }
      }
      `)}applyTo2d(t){let{imageData:{data:e}}=t;const s=new A(this.color).getSource(),r=this.alpha,i=s[0]*r,n=s[1]*r,o=s[2]*r,h=1-r;for(let l=0;l<e.length;l+=4){const c=e[l],u=e[l+1],g=e[l+2];let d,f,v;switch(this.mode){case"multiply":d=c*i/255,f=u*n/255,v=g*o/255;break;case"screen":d=255-(255-c)*(255-i)/255,f=255-(255-u)*(255-n)/255,v=255-(255-g)*(255-o)/255;break;case"add":d=c+i,f=u+n,v=g+o;break;case"difference":d=Math.abs(c-i),f=Math.abs(u-n),v=Math.abs(g-o);break;case"subtract":d=c-i,f=u-n,v=g-o;break;case"darken":d=Math.min(c,i),f=Math.min(u,n),v=Math.min(g,o);break;case"lighten":d=Math.max(c,i),f=Math.max(u,n),v=Math.max(g,o);break;case"overlay":d=i<128?2*c*i/255:255-2*(255-c)*(255-i)/255,f=n<128?2*u*n/255:255-2*(255-u)*(255-n)/255,v=o<128?2*g*o/255:255-2*(255-g)*(255-o)/255;break;case"exclusion":d=i+c-2*i*c/255,f=n+u-2*n*u/255,v=o+g-2*o*g/255;break;case"tint":d=i+c*h,f=n+u*h,v=o+g*h}e[l]=d,e[l+1]=f,e[l+2]=v}}sendUniformData(t,e){const s=new A(this.color).getSource();s[0]=this.alpha*s[0]/255,s[1]=this.alpha*s[1]/255,s[2]=this.alpha*s[2]/255,s[3]=this.alpha,t.uniform4fv(e.uColor,s)}}p(ke,"defaults",{color:"#F95C63",mode:"multiply",alpha:1}),p(ke,"type","BlendColor"),p(ke,"uniformLocations",["uColor"]),T.setClass(ke);const dh={multiply:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform sampler2D uImage;
    uniform vec4 uColor;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      vec4 color2 = texture2D(uImage, vTexCoord2);
      color.rgba *= color2.rgba;
      gl_FragColor = color;
    }
    `,mask:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform sampler2D uImage;
    uniform vec4 uColor;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      vec4 color2 = texture2D(uImage, vTexCoord2);
      color.a = color2.a;
      gl_FragColor = color;
    }
    `},fh=["type","image"];class Me extends U{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return dh[this.mode]}getVertexSource(){return`
    attribute vec2 aPosition;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    uniform mat3 uTransformMatrix;
    void main() {
      vTexCoord = aPosition;
      vTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;
      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
    }
    `}applyToWebGL(t){const e=t.context,s=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,s,e.TEXTURE1),super.applyToWebGL(t),this.unbindAdditionalTexture(e,e.TEXTURE1)}createTexture(t,e){return t.getCachedTexture(e.cacheKey,e.getElement())}calculateMatrix(){const t=this.image,{width:e,height:s}=t.getElement();return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/s,1]}applyTo2d(t){let{imageData:{data:e,width:s,height:r},filterBackend:{resources:i}}=t;const n=this.image;i.blendImage||(i.blendImage=Bt());const o=i.blendImage,h=o.getContext("2d");o.width!==s||o.height!==r?(o.width=s,o.height=r):h.clearRect(0,0,s,r),h.setTransform(n.scaleX,0,0,n.scaleY,n.left,n.top),h.drawImage(n.getElement(),0,0,s,r);const l=h.getImageData(0,0,s,r).data;for(let c=0;c<e.length;c+=4){const u=e[c],g=e[c+1],d=e[c+2],f=e[c+3],v=l[c],y=l[c+1],x=l[c+2],C=l[c+3];switch(this.mode){case"multiply":e[c]=u*v/255,e[c+1]=g*y/255,e[c+2]=d*x/255,e[c+3]=f*C/255;break;case"mask":e[c+3]=C}}}sendUniformData(t,e){const s=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,s)}toObject(){return m(m({},super.toObject()),{},{image:this.image&&this.image.toObject()})}static async fromObject(t,e){let{type:s,image:r}=t,i=I(t,fh);return lt.fromObject(r,e).then(n=>new this(m(m({},i),{},{image:n})))}}p(Me,"type","BlendImage"),p(Me,"defaults",{mode:"multiply",alpha:1}),p(Me,"uniformLocations",["uTransformMatrix","uImage"]),T.setClass(Me);class Ee extends U{getFragmentSource(){return`
    precision highp float;
    uniform sampler2D uTexture;
    uniform vec2 uDelta;
    varying vec2 vTexCoord;
    const float nSamples = 15.0;
    vec3 v3offset = vec3(12.9898, 78.233, 151.7182);
    float random(vec3 scale) {
      /* use the fragment position for a different seed per-pixel */
      return fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);
    }
    void main() {
      vec4 color = vec4(0.0);
      float totalC = 0.0;
      float totalA = 0.0;
      float offset = random(v3offset);
      for (float t = -nSamples; t <= nSamples; t++) {
        float percent = (t + offset - 0.5) / nSamples;
        vec4 sample = texture2D(uTexture, vTexCoord + uDelta * percent);
        float weight = 1.0 - abs(percent);
        float alpha = weight * sample.a;
        color.rgb += sample.rgb * alpha;
        color.a += alpha;
        totalA += weight;
        totalC += alpha;
      }
      gl_FragColor.rgb = color.rgb / totalC;
      gl_FragColor.a = color.a / totalA;
    }
  `}applyTo(t){Ys(t)?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){let{imageData:{data:e,width:s,height:r}}=t;this.aspectRatio=s/r,this.horizontal=!0;let i=this.getBlurValue()*s;const n=new Uint8ClampedArray(e),o=15,h=4*s;for(let l=0;l<e.length;l+=4){let c=0,u=0,g=0,d=0,f=0;const v=l-l%h,y=v+h;for(let x=-14;x<o;x++){const C=x/o,w=4*Math.floor(i*C),S=1-Math.abs(C);let b=l+w;b<v?b=v:b>y&&(b=y);const D=e[b+3]*S;c+=e[b]*D,u+=e[b+1]*D,g+=e[b+2]*D,d+=D,f+=S}n[l]=c/d,n[l+1]=u/d,n[l+2]=g/d,n[l+3]=d/f}this.horizontal=!1,i=this.getBlurValue()*r;for(let l=0;l<n.length;l+=4){let c=0,u=0,g=0,d=0,f=0;const v=l%h,y=n.length-h+v;for(let x=-14;x<o;x++){const C=x/o,w=Math.floor(i*C)*h,S=1-Math.abs(C);let b=l+w;b<v?b=v:b>y&&(b=y);const D=n[b+3]*S;c+=n[b]*D,u+=n[b+1]*D,g+=n[b+2]*D,d+=D,f+=S}e[l]=c/d,e[l+1]=u/d,e[l+2]=g/d,e[l+3]=d/f}}sendUniformData(t,e){const s=this.chooseRightDelta();t.uniform2fv(e.uDelta,s)}isNeutralState(){return this.blur===0}getBlurValue(){let t=1;const{horizontal:e,aspectRatio:s}=this;return e?s>1&&(t=1/s):s<1&&(t=s),t*this.blur*.12}chooseRightDelta(){const t=this.getBlurValue();return this.horizontal?[t,0]:[0,t]}}p(Ee,"type","Blur"),p(Ee,"defaults",{blur:0}),p(Ee,"uniformLocations",["uDelta"]),T.setClass(Ee);class je extends U{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uBrightness;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color.rgb += uBrightness;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=Math.round(255*this.brightness);for(let r=0;r<e.length;r+=4)e[r]+=s,e[r+1]+=s,e[r+2]+=s}isNeutralState(){return this.brightness===0}sendUniformData(t,e){t.uniform1f(e.uBrightness,this.brightness)}}p(je,"type","Brightness"),p(je,"defaults",{brightness:0}),p(je,"uniformLocations",["uBrightness"]),T.setClass(je);const wn={matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],colorsOnly:!0};class se extends U{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  varying vec2 vTexCoord;
  uniform mat4 uColorMatrix;
  uniform vec4 uConstants;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color *= uColorMatrix;
    color += uConstants;
    gl_FragColor = color;
  }`}applyTo2d(t){const e=t.imageData.data,s=this.matrix,r=this.colorsOnly;for(let i=0;i<e.length;i+=4){const n=e[i],o=e[i+1],h=e[i+2];if(e[i]=n*s[0]+o*s[1]+h*s[2]+255*s[4],e[i+1]=n*s[5]+o*s[6]+h*s[7]+255*s[9],e[i+2]=n*s[10]+o*s[11]+h*s[12]+255*s[14],!r){const l=e[i+3];e[i]+=l*s[3],e[i+1]+=l*s[8],e[i+2]+=l*s[13],e[i+3]=n*s[15]+o*s[16]+h*s[17]+l*s[18]+255*s[19]}}}sendUniformData(t,e){const s=this.matrix,r=[s[0],s[1],s[2],s[3],s[5],s[6],s[7],s[8],s[10],s[11],s[12],s[13],s[15],s[16],s[17],s[18]],i=[s[4],s[9],s[14],s[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,r),t.uniform4fv(e.uConstants,i)}toObject(){return m(m({},super.toObject()),{},{matrix:[...this.matrix]})}}function ne(a,t){var e;const s=(p(e=class extends se{toObject(){return{type:this.type,colorsOnly:this.colorsOnly}}},"type",a),p(e,"defaults",{colorsOnly:!1,matrix:t}),e);return T.setClass(s,a),s}p(se,"type","ColorMatrix"),p(se,"defaults",wn),p(se,"uniformLocations",["uColorMatrix","uConstants"]),T.setClass(se);const ph=ne("Brownie",[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0]),mh=ne("Vintage",[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0]),vh=ne("Kodachrome",[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0]),yh=ne("Technicolor",[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0]),_h=ne("Polaroid",[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0]),xh=ne("Sepia",[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0]),Ch=ne("BlackWhite",[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]);class yr extends U{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(t),this.subFilters=t.subFilters||[]}applyTo(t){Ys(t)&&(t.passes+=this.subFilters.length-1),this.subFilters.forEach(e=>{e.applyTo(t)})}toObject(){return{type:this.type,subFilters:this.subFilters.map(t=>t.toObject())}}isNeutralState(){return!this.subFilters.some(t=>!t.isNeutralState())}static fromObject(t,e){return Promise.all((t.subFilters||[]).map(s=>T.getClass(s.type).fromObject(s,e))).then(s=>new this({subFilters:s}))}}p(yr,"type","Composed"),T.setClass(yr);class Pe extends U{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uContrast;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));
    color.rgb = contrastF * (color.rgb - 0.5) + 0.5;
    gl_FragColor = color;
  }`}isNeutralState(){return this.contrast===0}applyTo2d(t){let{imageData:{data:e}}=t;const s=Math.floor(255*this.contrast),r=259*(s+255)/(255*(259-s));for(let i=0;i<e.length;i+=4)e[i]=r*(e[i]-128)+128,e[i+1]=r*(e[i+1]-128)+128,e[i+2]=r*(e[i+2]-128)+128}sendUniformData(t,e){t.uniform1f(e.uContrast,this.contrast)}}p(Pe,"type","Contrast"),p(Pe,"defaults",{contrast:0}),p(Pe,"uniformLocations",["uContrast"]),T.setClass(Pe);const bh={Convolute_3_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[9];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 3.0; h+=1.0) {
        for (float w = 0.0; w < 3.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_3_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[9];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 3.0; h+=1.0) {
        for (float w = 0.0; w < 3.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_5_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[25];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 5.0; h+=1.0) {
        for (float w = 0.0; w < 5.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_5_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[25];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 5.0; h+=1.0) {
        for (float w = 0.0; w < 5.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_7_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[49];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 7.0; h+=1.0) {
        for (float w = 0.0; w < 7.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_7_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[49];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 7.0; h+=1.0) {
        for (float w = 0.0; w < 7.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_9_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[81];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 9.0; h+=1.0) {
        for (float w = 0.0; w < 9.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_9_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[81];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 9.0; h+=1.0) {
        for (float w = 0.0; w < 9.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `};class Ae extends U{getCacheKey(){return"".concat(this.type,"_").concat(Math.sqrt(this.matrix.length),"_").concat(this.opaque?1:0)}getFragmentSource(){return bh[this.getCacheKey()]}applyTo2d(t){const e=t.imageData,s=e.data,r=this.matrix,i=Math.round(Math.sqrt(r.length)),n=Math.floor(i/2),o=e.width,h=e.height,l=t.ctx.createImageData(o,h),c=l.data,u=this.opaque?1:0;let g,d,f,v,y,x,C,w,S,b,D,M,O;for(D=0;D<h;D++)for(b=0;b<o;b++){for(y=4*(D*o+b),g=0,d=0,f=0,v=0,O=0;O<i;O++)for(M=0;M<i;M++)C=D+O-n,x=b+M-n,C<0||C>=h||x<0||x>=o||(w=4*(C*o+x),S=r[O*i+M],g+=s[w]*S,d+=s[w+1]*S,f+=s[w+2]*S,u||(v+=s[w+3]*S));c[y]=g,c[y+1]=d,c[y+2]=f,c[y+3]=u?s[y+3]:v}t.imageData=l}sendUniformData(t,e){t.uniform1fv(e.uMatrix,this.matrix)}toObject(){return m(m({},super.toObject()),{},{opaque:this.opaque,matrix:[...this.matrix]})}}p(Ae,"type","Convolute"),p(Ae,"defaults",{opaque:!1,matrix:[0,0,0,0,1,0,0,0,0]}),p(Ae,"uniformLocations",["uMatrix","uOpaque","uHalfSize","uSize"]),T.setClass(Ae);const Tn="Gamma";class Fe extends U{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform vec3 uGamma;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    vec3 correction = (1.0 / uGamma);
    color.r = pow(color.r, correction.r);
    color.g = pow(color.g, correction.g);
    color.b = pow(color.b, correction.b);
    gl_FragColor = color;
    gl_FragColor.rgb *= color.a;
  }
`}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(t),this.gamma=t.gamma||this.constructor.defaults.gamma.concat()}applyTo2d(t){let{imageData:{data:e}}=t;const s=this.gamma,r=1/s[0],i=1/s[1],n=1/s[2];this.rgbValues||(this.rgbValues={r:new Uint8Array(256),g:new Uint8Array(256),b:new Uint8Array(256)});const o=this.rgbValues;for(let h=0;h<256;h++)o.r[h]=255*Math.pow(h/255,r),o.g[h]=255*Math.pow(h/255,i),o.b[h]=255*Math.pow(h/255,n);for(let h=0;h<e.length;h+=4)e[h]=o.r[e[h]],e[h+1]=o.g[e[h+1]],e[h+2]=o.b[e[h+2]]}sendUniformData(t,e){t.uniform3fv(e.uGamma,this.gamma)}isNeutralState(){const{gamma:t}=this;return t[0]===1&&t[1]===1&&t[2]===1}toObject(){return{type:Tn,gamma:this.gamma.concat()}}}p(Fe,"type",Tn),p(Fe,"defaults",{gamma:[1,1,1]}),p(Fe,"uniformLocations",["uGamma"]),T.setClass(Fe);const Sh={average:`
    precision highp float;
    uniform sampler2D uTexture;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      float average = (color.r + color.b + color.g) / 3.0;
      gl_FragColor = vec4(average, average, average, color.a);
    }
    `,lightness:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform int uMode;
    varying vec2 vTexCoord;
    void main() {
      vec4 col = texture2D(uTexture, vTexCoord);
      float average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;
      gl_FragColor = vec4(average, average, average, col.a);
    }
    `,luminosity:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform int uMode;
    varying vec2 vTexCoord;
    void main() {
      vec4 col = texture2D(uTexture, vTexCoord);
      float average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;
      gl_FragColor = vec4(average, average, average, col.a);
    }
    `};class Le extends U{applyTo2d(t){let{imageData:{data:e}}=t;for(let s,r=0;r<e.length;r+=4){const i=e[r],n=e[r+1],o=e[r+2];switch(this.mode){case"average":s=(i+n+o)/3;break;case"lightness":s=(Math.min(i,n,o)+Math.max(i,n,o))/2;break;case"luminosity":s=.21*i+.72*n+.07*o}e[r+2]=e[r+1]=e[r]=s}}getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return Sh[this.mode]}sendUniformData(t,e){t.uniform1i(e.uMode,1)}isNeutralState(){return!1}}p(Le,"type","Grayscale"),p(Le,"defaults",{mode:"average"}),p(Le,"uniformLocations",["uMode"]),T.setClass(Le);const wh=m(m({},wn),{},{rotation:0});class _s extends se{calculateMatrix(){const t=this.rotation*Math.PI,e=Lt(t),s=Rt(t),r=1/3,i=Math.sqrt(r)*s,n=1-e;this.matrix=[e+n/3,r*n-i,r*n+i,0,0,r*n+i,e+r*n,r*n-i,0,0,r*n-i,r*n+i,e+r*n,0,0,0,0,0,1,0]}isNeutralState(){return this.rotation===0}applyTo(t){this.calculateMatrix(),super.applyTo(t)}toObject(){return{type:this.type,rotation:this.rotation}}}p(_s,"type","HueRotation"),p(_s,"defaults",wh),T.setClass(_s);class Re extends U{applyTo2d(t){let{imageData:{data:e}}=t;for(let s=0;s<e.length;s+=4)e[s]=255-e[s],e[s+1]=255-e[s+1],e[s+2]=255-e[s+2],this.alpha&&(e[s+3]=255-e[s+3])}getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform int uInvert;
  uniform int uAlpha;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    if (uInvert == 1) {
      if (uAlpha == 1) {
        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,1.0 -color.a);
      } else {
        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);
      }
    } else {
      gl_FragColor = color;
    }
  }
`}isNeutralState(){return!this.invert}sendUniformData(t,e){t.uniform1i(e.uInvert,Number(this.invert)),t.uniform1i(e.uAlpha,Number(this.alpha))}}p(Re,"type","Invert"),p(Re,"defaults",{alpha:!1,invert:!0}),p(Re,"uniformLocations",["uInvert","uAlpha"]),T.setClass(Re);class Be extends U{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uStepH;
  uniform float uNoise;
  uniform float uSeed;
  varying vec2 vTexCoord;
  float rand(vec2 co, float seed, float vScale) {
    return fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);
  }
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=this.noise;for(let r=0;r<e.length;r+=4){const i=(.5-Math.random())*s;e[r]+=i,e[r+1]+=i,e[r+2]+=i}}sendUniformData(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())}isNeutralState(){return this.noise===0}}p(Be,"type","Noise"),p(Be,"defaults",{noise:0}),p(Be,"uniformLocations",["uNoise","uSeed"]),T.setClass(Be);class Ie extends U{applyTo2d(t){let{imageData:{data:e,width:s,height:r}}=t;for(let i=0;i<r;i+=this.blocksize)for(let n=0;n<s;n+=this.blocksize){const o=4*i*s+4*n,h=e[o],l=e[o+1],c=e[o+2],u=e[o+3];for(let g=i;g<Math.min(i+this.blocksize,r);g++)for(let d=n;d<Math.min(n+this.blocksize,s);d++){const f=4*g*s+4*d;e[f]=h,e[f+1]=l,e[f+2]=c,e[f+3]=u}}}isNeutralState(){return this.blocksize===1}getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uBlocksize;
  uniform float uStepW;
  uniform float uStepH;
  varying vec2 vTexCoord;
  void main() {
    float blockW = uBlocksize * uStepW;
    float blockH = uBlocksize * uStepH;
    int posX = int(vTexCoord.x / blockW);
    int posY = int(vTexCoord.y / blockH);
    float fposX = float(posX);
    float fposY = float(posY);
    vec2 squareCoords = vec2(fposX * blockW, fposY * blockH);
    vec4 color = texture2D(uTexture, squareCoords);
    gl_FragColor = color;
  }
`}sendUniformData(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}p(Ie,"type","Pixelate"),p(Ie,"defaults",{blocksize:4}),p(Ie,"uniformLocations",["uBlocksize"]),T.setClass(Ie);class Xe extends U{getFragmentSource(){return`
precision highp float;
uniform sampler2D uTexture;
uniform vec4 uLow;
uniform vec4 uHigh;
varying vec2 vTexCoord;
void main() {
  gl_FragColor = texture2D(uTexture, vTexCoord);
  if(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {
    gl_FragColor.a = 0.0;
  }
}
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=255*this.distance,r=new A(this.color).getSource(),i=[r[0]-s,r[1]-s,r[2]-s],n=[r[0]+s,r[1]+s,r[2]+s];for(let o=0;o<e.length;o+=4){const h=e[o],l=e[o+1],c=e[o+2];h>i[0]&&l>i[1]&&c>i[2]&&h<n[0]&&l<n[1]&&c<n[2]&&(e[o+3]=0)}}sendUniformData(t,e){const s=new A(this.color).getSource(),r=this.distance,i=[0+s[0]/255-r,0+s[1]/255-r,0+s[2]/255-r,1],n=[s[0]/255+r,s[1]/255+r,s[2]/255+r,1];t.uniform4fv(e.uLow,i),t.uniform4fv(e.uHigh,n)}}p(Xe,"type","RemoveColor"),p(Xe,"defaults",{color:"#FFFFFF",distance:.02,useAlpha:!1}),p(Xe,"uniformLocations",["uLow","uHigh"]),T.setClass(Xe);class We extends U{sendUniformData(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)}getFilterWindow(){const t=this.tempScale;return Math.ceil(this.lanczosLobes/t)}getCacheKey(){const t=this.getFilterWindow();return"".concat(this.type,"_").concat(t)}getFragmentSource(){const t=this.getFilterWindow();return this.generateShader(t)}getTaps(){const t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,s=this.getFilterWindow(),r=new Array(s);for(let i=1;i<=s;i++)r[i-1]=t(i*e);return r}generateShader(t){const e=new Array(t);for(let s=1;s<=t;s++)e[s-1]="".concat(s,".0 * uDelta");return`
      precision highp float;
      uniform sampler2D uTexture;
      uniform vec2 uDelta;
      varying vec2 vTexCoord;
      uniform float uTaps[`.concat(t,`];
      void main() {
        vec4 color = texture2D(uTexture, vTexCoord);
        float sum = 1.0;
        `).concat(e.map((s,r)=>`
              color += texture2D(uTexture, vTexCoord + `.concat(s,") * uTaps[").concat(r,"] + texture2D(uTexture, vTexCoord - ").concat(s,") * uTaps[").concat(r,`];
              sum += 2.0 * uTaps[`).concat(r,`];
            `)).join(`
`),`
        gl_FragColor = color / sum;
      }
    `)}applyToForWebgl(t){t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,super.applyTo(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,super.applyTo(t),t.sourceHeight=t.destinationHeight}applyTo(t){Ys(t)?this.applyToForWebgl(t):this.applyTo2d(t)}isNeutralState(){return this.scaleX===1&&this.scaleY===1}lanczosCreate(t){return e=>{if(e>=t||e<=-t)return 0;if(e<11920929e-14&&e>-11920929e-14)return 1;const s=(e*=Math.PI)/t;return Math.sin(e)/e*Math.sin(s)/s}}applyTo2d(t){const e=t.imageData,s=this.scaleX,r=this.scaleY;this.rcpScaleX=1/s,this.rcpScaleY=1/r;const i=e.width,n=e.height,o=Math.round(i*s),h=Math.round(n*r);let l;l=this.resizeType==="sliceHack"?this.sliceByTwo(t,i,n,o,h):this.resizeType==="hermite"?this.hermiteFastResize(t,i,n,o,h):this.resizeType==="bilinear"?this.bilinearFiltering(t,i,n,o,h):this.resizeType==="lanczos"?this.lanczosResize(t,i,n,o,h):new ImageData(o,h),t.imageData=l}sliceByTwo(t,e,s,r,i){const n=t.imageData,o=.5;let h=!1,l=!1,c=e*o,u=s*o;const g=t.filterBackend.resources;let d=0,f=0;const v=e;let y=0;g.sliceByTwo||(g.sliceByTwo=Bt());const x=g.sliceByTwo;(x.width<1.5*e||x.height<s)&&(x.width=1.5*e,x.height=s);const C=x.getContext("2d");for(C.clearRect(0,0,1.5*e,s),C.putImageData(n,0,0),r=Math.floor(r),i=Math.floor(i);!h||!l;)e=c,s=u,r<Math.floor(c*o)?c=Math.floor(c*o):(c=r,h=!0),i<Math.floor(u*o)?u=Math.floor(u*o):(u=i,l=!0),C.drawImage(x,d,f,e,s,v,y,c,u),d=v,f=y,y+=u;return C.getImageData(d,f,r,i)}lanczosResize(t,e,s,r,i){const n=t.imageData.data,o=t.ctx.createImageData(r,i),h=o.data,l=this.lanczosCreate(this.lanczosLobes),c=this.rcpScaleX,u=this.rcpScaleY,g=2/this.rcpScaleX,d=2/this.rcpScaleY,f=Math.ceil(c*this.lanczosLobes/2),v=Math.ceil(u*this.lanczosLobes/2),y={},x={x:0,y:0},C={x:0,y:0};return function w(S){let b,D,M,O,F,L,q,V,P,W,at;for(x.x=(S+.5)*c,C.x=Math.floor(x.x),b=0;b<i;b++){for(x.y=(b+.5)*u,C.y=Math.floor(x.y),F=0,L=0,q=0,V=0,P=0,D=C.x-f;D<=C.x+f;D++)if(!(D<0||D>=e)){W=Math.floor(1e3*Math.abs(D-x.x)),y[W]||(y[W]={});for(let et=C.y-v;et<=C.y+v;et++)et<0||et>=s||(at=Math.floor(1e3*Math.abs(et-x.y)),y[W][at]||(y[W][at]=l(Math.sqrt(Math.pow(W*g,2)+Math.pow(at*d,2))/1e3)),M=y[W][at],M>0&&(O=4*(et*e+D),F+=M,L+=M*n[O],q+=M*n[O+1],V+=M*n[O+2],P+=M*n[O+3]))}O=4*(b*r+S),h[O]=L/F,h[O+1]=q/F,h[O+2]=V/F,h[O+3]=P/F}return++S<r?w(S):o}(0)}bilinearFiltering(t,e,s,r,i){let n,o,h,l,c,u,g,d,f,v,y,x,C,w=0;const S=this.rcpScaleX,b=this.rcpScaleY,D=4*(e-1),M=t.imageData.data,O=t.ctx.createImageData(r,i),F=O.data;for(g=0;g<i;g++)for(d=0;d<r;d++)for(c=Math.floor(S*d),u=Math.floor(b*g),f=S*d-c,v=b*g-u,C=4*(u*e+c),y=0;y<4;y++)n=M[C+y],o=M[C+4+y],h=M[C+D+y],l=M[C+D+4+y],x=n*(1-f)*(1-v)+o*f*(1-v)+h*v*(1-f)+l*f*v,F[w++]=x;return O}hermiteFastResize(t,e,s,r,i){const n=this.rcpScaleX,o=this.rcpScaleY,h=Math.ceil(n/2),l=Math.ceil(o/2),c=t.imageData.data,u=t.ctx.createImageData(r,i),g=u.data;for(let d=0;d<i;d++)for(let f=0;f<r;f++){const v=4*(f+d*r);let y=0,x=0,C=0,w=0,S=0,b=0,D=0;const M=(d+.5)*o;for(let O=Math.floor(d*o);O<(d+1)*o;O++){const F=Math.abs(M-(O+.5))/l,L=(f+.5)*n,q=F*F;for(let V=Math.floor(f*n);V<(f+1)*n;V++){let P=Math.abs(L-(V+.5))/h;const W=Math.sqrt(q+P*P);W>1&&W<-1||(y=2*W*W*W-3*W*W+1,y>0&&(P=4*(V+O*e),D+=y*c[P+3],C+=y,c[P+3]<255&&(y=y*c[P+3]/250),w+=y*c[P],S+=y*c[P+1],b+=y*c[P+2],x+=y))}}g[v]=w/x,g[v+1]=S/x,g[v+2]=b/x,g[v+3]=D/C}return u}}p(We,"type","Resize"),p(We,"defaults",{resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3}),p(We,"uniformLocations",["uDelta","uTaps"]),T.setClass(We);class Ye extends U{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uSaturation;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float rgMax = max(color.r, color.g);
    float rgbMax = max(rgMax, color.b);
    color.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;
    color.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;
    color.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=-this.saturation;for(let r=0;r<e.length;r+=4){const i=e[r],n=e[r+1],o=e[r+2],h=Math.max(i,n,o);e[r]+=h!==i?(h-i)*s:0,e[r+1]+=h!==n?(h-n)*s:0,e[r+2]+=h!==o?(h-o)*s:0}}sendUniformData(t,e){t.uniform1f(e.uSaturation,-this.saturation)}isNeutralState(){return this.saturation===0}}p(Ye,"type","Saturation"),p(Ye,"defaults",{saturation:0}),p(Ye,"uniformLocations",["uSaturation"]),T.setClass(Ye);class Ve extends U{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uVibrance;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float max = max(color.r, max(color.g, color.b));
    float avg = (color.r + color.g + color.b) / 3.0;
    float amt = (abs(max - avg) * 2.0) * uVibrance;
    color.r += max != color.r ? (max - color.r) * amt : 0.00;
    color.g += max != color.g ? (max - color.g) * amt : 0.00;
    color.b += max != color.b ? (max - color.b) * amt : 0.00;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=-this.vibrance;for(let r=0;r<e.length;r+=4){const i=e[r],n=e[r+1],o=e[r+2],h=Math.max(i,n,o),l=(i+n+o)/3,c=2*Math.abs(h-l)/255*s;e[r]+=h!==i?(h-i)*c:0,e[r+1]+=h!==n?(h-n)*c:0,e[r+2]+=h!==o?(h-o)*c:0}}sendUniformData(t,e){t.uniform1f(e.uVibrance,-this.vibrance)}isNeutralState(){return this.vibrance===0}}p(Ve,"type","Vibrance"),p(Ve,"defaults",{vibrance:0}),p(Ve,"uniformLocations",["uVibrance"]),T.setClass(Ve);var pe=Object.freeze({__proto__:null,BaseFilter:U,BlackWhite:Ch,BlendColor:ke,BlendImage:Me,Blur:Ee,Brightness:je,Brownie:ph,ColorMatrix:se,Composed:yr,Contrast:Pe,Convolute:Ae,Gamma:Fe,Grayscale:Le,HueRotation:_s,Invert:Re,Kodachrome:vh,Noise:Be,Pixelate:Ie,Polaroid:_h,RemoveColor:Xe,Resize:We,Saturation:Ye,Sepia:xh,Technicolor:yh,Vibrance:Ve,Vintage:mh});function Th(a){const t=a.parentElement,e=(t==null?void 0:t.clientWidth)||window.innerWidth,s=(t==null?void 0:t.clientHeight)||window.innerHeight,r=Math.max(e,100),i=Math.max(s,100);return new mr(a,{width:r,height:i,backgroundColor:"#2a2a2a"})}async function Oh(a,t){const e=await lt.fromURL(t,{crossOrigin:"anonymous"});a.clear();const s=a.getWidth(),r=a.getHeight(),i=e.width||1,n=e.height||1,o=Math.min(s/i,r/n);e.scale(o),e.set({left:s/2,top:r/2,originX:"center",originY:"center"}),On=e,a.add(e),a.setActiveObject(e),a.renderAll()}let On=null;function Dh(){return On}let R=null;function kh(a){R=a}function er(a,t){if(!R)return;R.filters=R.filters||[];const e=R.filters.findIndex(r=>r instanceof pe.Brightness),s=new pe.Brightness({brightness:t-1});e>=0?R.filters[e]=s:R.filters.push(s),R.applyFilters(),a.renderAll()}function sr(a,t){if(!R)return;R.filters=R.filters||[];const e=R.filters.findIndex(r=>r instanceof pe.Contrast),s=new pe.Contrast({contrast:t-1});e>=0?R.filters[e]=s:R.filters.push(s),R.applyFilters(),a.renderAll()}function rr(a,t){if(!R)return;R.filters=R.filters||[];const e=R.filters.findIndex(r=>r instanceof pe.Saturation),s=new pe.Saturation({saturation:t-1});e>=0?R.filters[e]=s:R.filters.push(s),R.applyFilters(),a.renderAll()}function ir(a,t){R&&(R.rotate(t),a.renderAll())}function Ph({data:a}){const t=pt.useRef(null),e=pt.useRef(null),[s,r]=pt.useState(a.viewer.configs.brightness),[i,n]=pt.useState(a.viewer.configs.contrast),[o,h]=pt.useState(a.viewer.configs.saturation),[l,c]=pt.useState(a.viewer.configs.rotation);pt.useEffect(()=>!t.current||e.current?void 0:((async()=>{if(await new Promise(S=>setTimeout(S,100)),!t.current||e.current)return;const x=Th(t.current);e.current=x;const C=a.viewer.imageUrl;await Oh(x,C);const w=Dh();w&&(kh(w),er(x,s),sr(x,i),rr(x,o),ir(x,l))})(),()=>{e.current&&(e.current.dispose(),e.current=null)}),[a]),pt.useEffect(()=>{e.current&&er(e.current,s)},[s]),pt.useEffect(()=>{e.current&&sr(e.current,i)},[i]),pt.useEffect(()=>{e.current&&rr(e.current,o)},[o]),pt.useEffect(()=>{e.current&&ir(e.current,l)},[l]);const u=y=>{e.current&&(r(y),er(e.current,y))},g=y=>{e.current&&(n(y),sr(e.current,y))},d=y=>{e.current&&(h(y),rr(e.current,y))},f=y=>{e.current&&(c(y),ir(e.current,y))},v=async()=>{await kn(a.id,{brightness:s,contrast:i,saturation:o,rotation:l})};return G.jsxs("div",{className:"image-viewer",children:[G.jsx("canvas",{ref:t,id:"image-canvas"}),G.jsxs("div",{className:"controls",children:[G.jsxs("div",{className:"control-group",children:[G.jsx("label",{children:"Brightness:"}),G.jsx("input",{type:"range",min:"0",max:"2",step:"0.1",value:s,onChange:y=>u(parseFloat(y.target.value))}),G.jsx("span",{children:s.toFixed(1)})]}),G.jsxs("div",{className:"control-group",children:[G.jsx("label",{children:"Contrast:"}),G.jsx("input",{type:"range",min:"0",max:"2",step:"0.1",value:i,onChange:y=>g(parseFloat(y.target.value))}),G.jsx("span",{children:i.toFixed(1)})]}),G.jsxs("div",{className:"control-group",children:[G.jsx("label",{children:"Saturation:"}),G.jsx("input",{type:"range",min:"0",max:"2",step:"0.1",value:o,onChange:y=>d(parseFloat(y.target.value))}),G.jsx("span",{children:o.toFixed(1)})]}),G.jsxs("div",{className:"control-group",children:[G.jsx("label",{children:"Rotation:"}),G.jsx("input",{type:"range",min:"0",max:"360",step:"15",value:l,onChange:y=>f(parseFloat(y.target.value))}),G.jsxs("span",{children:[l,"°"]})]}),G.jsx("button",{className:"save-button",onClick:v,children:"Save Settings"})]})]})}export{Ph as default};
