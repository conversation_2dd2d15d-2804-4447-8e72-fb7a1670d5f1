import { useEffect, useState } from "react";
import {
  setAnnotationTool,
  clearAnnotations,
  disableAnnotationMode,
  type AnnotationTool,
} from "@/lib/fabric";
import type { FabricToolbarProps } from "@/shared/types";

export default function FabricToolbar({
  initialValues,
  onSave,
  cssFilterMode,
  fabricFilterMode,
  enableAnnotations = false,
  containerId,
}: FabricToolbarProps) {
  const [brightness, setBrightness] = useState(initialValues.brightness || 1);
  const [contrast, setContrast] = useState(initialValues.contrast || 1);
  const [saturation, setSaturation] = useState(initialValues.saturation || 1);
  const [rotation, setRotation] = useState(initialValues.rotation || 0);
  const [selectedTool, setSelectedTool] = useState<AnnotationTool | null>(null);

  useEffect(() => {
    setBrightness(initialValues.brightness || 1);
    setContrast(initialValues.contrast || 1);
    setSaturation(initialValues.saturation || 1);
    setRotation(initialValues.rotation || 0);
  }, [initialValues]);

  const handleBrightnessChange = (value: number) => {
    console.log("🎛️ FabricToolbar brightness change:", value);
    setBrightness(value);

    if (fabricFilterMode) {
      console.log("🖼️ Using fabricFilterMode for ImageViewer");
      // For ImageViewer, trigger the callback to update internal state
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateCallback = (window as any).imageViewerUpdateBrightness;
      console.log("📞 Callback found:", !!updateCallback);
      if (updateCallback) {
        console.log("✅ Calling ImageViewer brightness update with:", value);
        updateCallback(value);
      } else {
        console.error("❌ No brightness update callback found");
      }
    } else if (cssFilterMode) {
      console.log("🎨 Using cssFilterMode for StackViewer");
      // Use CSS filters for StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        const currentFilter = element.style.filter;
        const newFilter =
          currentFilter.replace(/brightness\([^)]*\)/, "") +
          ` brightness(${value})`;
        element.style.filter = newFilter.trim();
      }
    }
  };

  const handleContrastChange = (value: number) => {
    setContrast(value);

    if (fabricFilterMode) {
      // For ImageViewer, trigger the callback to update internal state
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateCallback = (window as any).imageViewerUpdateContrast;
      if (updateCallback) {
        updateCallback(value);
      }
    } else if (cssFilterMode) {
      // Use CSS filters for StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        const currentFilter = element.style.filter;
        const newFilter =
          currentFilter.replace(/contrast\([^)]*\)/, "") +
          ` contrast(${value})`;
        element.style.filter = newFilter.trim();
      }
    }
  };

  const handleSaturationChange = (value: number) => {
    setSaturation(value);

    if (fabricFilterMode) {
      // For ImageViewer, trigger the callback to update internal state
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateCallback = (window as any).imageViewerUpdateSaturation;
      if (updateCallback) {
        updateCallback(value);
      }
    } else if (cssFilterMode) {
      // Use CSS filters for StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        const currentFilter = element.style.filter;
        const newFilter =
          currentFilter.replace(/saturate\([^)]*\)/, "") +
          ` saturate(${value})`;
        element.style.filter = newFilter.trim();
      }
    }
  };

  const handleRotationChange = (value: number) => {
    setRotation(value);

    if (fabricFilterMode) {
      // For ImageViewer, trigger the callback to update internal state
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateCallback = (window as any).imageViewerUpdateRotation;
      if (updateCallback) {
        updateCallback(value);
      }
    } else if (cssFilterMode) {
      // Use CSS transform for StackViewer
      const element = cssFilterMode.targetElement();
      if (element) {
        element.style.transform = `rotate(${value}deg)`;
      }
    }
  };

  const handleAnnotationTool = (tool: AnnotationTool) => {
    if (!enableAnnotations || !containerId) return;

    if (selectedTool === tool) {
      disableAnnotationMode();
      setSelectedTool(null);
    } else {
      setAnnotationTool(tool, containerId);
      setSelectedTool(tool);
    }
  };

  const handleClearAnnotations = () => {
    if (!enableAnnotations || !containerId) return;

    clearAnnotations(containerId);
    setSelectedTool(null);
  };

  const handleSave = () => {
    if (onSave) {
      onSave({
        brightness,
        contrast,
        saturation,
        rotation,
      });
    }
  };

  return (
    <div className="fabric-toolbar-vertical">
      <div className="toolbar-header">
        <h5>Controls</h5>
      </div>

      <div className="control-item">
        <label>Brightness</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={brightness}
          onChange={(e) => handleBrightnessChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{brightness.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label>Contrast</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={contrast}
          onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{contrast.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label>Saturation</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={saturation}
          onChange={(e) => handleSaturationChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{saturation.toFixed(1)}</span>
      </div>

      <div className="control-item">
        <label>Rotation</label>
        <input
          type="range"
          min="0"
          max="360"
          step="15"
          value={rotation}
          onChange={(e) => handleRotationChange(parseFloat(e.target.value))}
          className="horizontal-slider"
        />
        <span>{rotation}°</span>
      </div>

      {enableAnnotations && (
        <>
          <div className="annotation-tools">
            <label>Annotations</label>
            <div className="tool-grid">
              <button
                className={`tool-btn ${
                  selectedTool === "rectangle" ? "active" : ""
                }`}
                onClick={() => handleAnnotationTool("rectangle")}
                title="Rectangle"
              >
                ▭
              </button>
              <button
                className={`tool-btn ${
                  selectedTool === "circle" ? "active" : ""
                }`}
                onClick={() => handleAnnotationTool("circle")}
                title="Circle"
              >
                ○
              </button>
              <button
                className={`tool-btn ${
                  selectedTool === "ellipse" ? "active" : ""
                }`}
                onClick={() => handleAnnotationTool("ellipse")}
                title="Ellipse"
              >
                ⬭
              </button>
              <button
                className={`tool-btn ${
                  selectedTool === "line" ? "active" : ""
                }`}
                onClick={() => handleAnnotationTool("line")}
                title="Line"
              >
                ╱
              </button>
              <button
                className={`tool-btn ${
                  selectedTool === "polygon" ? "active" : ""
                }`}
                onClick={() => handleAnnotationTool("polygon")}
                title="Polygon"
              >
                ⬟
              </button>
              <button
                className={`tool-btn ${
                  selectedTool === "triangle" ? "active" : ""
                }`}
                onClick={() => handleAnnotationTool("triangle")}
                title="Triangle"
              >
                △
              </button>
            </div>
            <button className="clear-btn" onClick={handleClearAnnotations}>
              Clear
            </button>
          </div>
        </>
      )}

      {onSave && (
        <button className="save-btn" onClick={handleSave}>
          Save
        </button>
      )}
    </div>
  );
}
