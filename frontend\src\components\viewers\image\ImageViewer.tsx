import { useRef, useEffect } from "react";
import { initializeAnnotations, removeAnnotations } from "@/lib/fabric";
import { saveImageConfig } from "@/shared/api";
import { MedicalImageViewerProps } from "@/shared/types";
import FabricToolbar from "@/components/toolbars/FabricToolbar";

export default function ImageViewer({ data }: MedicalImageViewerProps) {
  const imageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    if (!imageRef.current) return;

    const img = imageRef.current;
    img.src = data.viewer.imageUrl;

    const { brightness, contrast, saturation, rotation } = data.viewer.configs;
    img.style.filter = `brightness(${brightness}) contrast(${contrast}) saturate(${saturation})`;
    img.style.transform = `rotate(${rotation}deg)`;

    img.onload = () => {
      initializeAnnotations(img, "image-viewer-container");
      // Hide the original image since we're rendering it on Fabric.js canvas
      img.style.display = "none";
    };

    return () => {
      removeAnnotations("image-viewer-container");
    };
  }, [data]);

  const handleSave = async (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => {
    await saveImageConfig(data.id, values);
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <img
        ref={imageRef}
        alt="Medical Image"
        style={{
          maxWidth: "100%",
          maxHeight: "100%",
          objectFit: "contain",
          display: "block",
          margin: "0 auto",
        }}
      />

      <FabricToolbar
        initialValues={{
          brightness: data.viewer.configs.brightness,
          contrast: data.viewer.configs.contrast,
          saturation: data.viewer.configs.saturation,
          rotation: data.viewer.configs.rotation,
        }}
        onSave={handleSave}
        fabricFilterMode={{
          containerId: "image-viewer-container",
        }}
        enableAnnotations={true}
        containerId="image-viewer-container"
      />
    </div>
  );
}
