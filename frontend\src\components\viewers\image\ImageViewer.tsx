import { useRef, useEffect, useState, useCallback } from "react";
import { saveImageConfig } from "@/shared/api";
import { MedicalImageViewerProps } from "@/shared/types";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import * as fabric from "fabric";

export default function ImageViewer({ data }: MedicalImageViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const currentImageRef = useRef<fabric.FabricImage | null>(null);

  const [brightness, setBrightness] = useState(data.viewer.configs.brightness);
  const [contrast, setContrast] = useState(data.viewer.configs.contrast);
  const [saturation, setSaturation] = useState(data.viewer.configs.saturation);
  const [rotation, setRotation] = useState(data.viewer.configs.rotation);

  const createCanvas = (canvasElement: HTMLCanvasElement): fabric.Canvas => {
    const container = canvasElement.parentElement;
    const width = container?.clientWidth || window.innerWidth;
    const height = container?.clientHeight || window.innerHeight;

    const finalWidth = Math.max(width, 100);
    const finalHeight = Math.max(height, 100);

    const canvas = new fabric.Canvas(canvasElement, {
      width: finalWidth,
      height: finalHeight,
      backgroundColor: "#000",
    });

    return canvas;
  };

  const loadImageToCanvas = async (
    canvas: fabric.Canvas,
    imageUrl: string
  ): Promise<void> => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const img = await (fabric as any).FabricImage.fromURL(imageUrl, {
      crossOrigin: "anonymous",
    });

    canvas.clear();
    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();
    const imgWidth = img.width || 1;
    const imgHeight = img.height || 1;
    const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight);

    img.scale(scale);
    img.set({
      left: canvasWidth / 2,
      top: canvasHeight / 2,
      originX: "center",
      originY: "center",
      selectable: false,
      evented: false,
    });

    currentImageRef.current = img;
    canvas.add(img);
    canvas.renderAll();

    // Store reference for FabricToolbar
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerFabricCanvas = canvas;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (canvas as any).imageObject = img;
  };

  // Expose update functions for FabricToolbar
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerUpdateBrightness = setBrightness;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerUpdateContrast = setContrast;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerUpdateSaturation = setSaturation;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerUpdateRotation = setRotation;

    return () => {
      // Cleanup
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      delete (window as any).imageViewerUpdateBrightness;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      delete (window as any).imageViewerUpdateContrast;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      delete (window as any).imageViewerUpdateSaturation;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      delete (window as any).imageViewerUpdateRotation;
    };
  }, []);

  const applyFilters = useCallback(() => {
    if (!currentImageRef.current || !fabricCanvasRef.current) return;

    const img = currentImageRef.current;
    img.filters = [];

    // Apply brightness filter
    if (brightness !== 1) {
      img.filters.push(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        new (fabric as any).Image.filters.Brightness({
          brightness: brightness - 1,
        })
      );
    }

    // Apply contrast filter
    if (contrast !== 1) {
      img.filters.push(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        new (fabric as any).Image.filters.Contrast({
          contrast: contrast - 1,
        })
      );
    }

    // Apply saturation filter
    if (saturation !== 1) {
      img.filters.push(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        new (fabric as any).Image.filters.Saturation({
          saturation: saturation - 1,
        })
      );
    }

    // Apply rotation
    img.set("angle", rotation);

    img.applyFilters();
    fabricCanvasRef.current.renderAll();
  }, [brightness, contrast, saturation, rotation]);

  useEffect(() => {
    if (!canvasRef.current || fabricCanvasRef.current) return;

    const initializeCanvas = async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (!canvasRef.current || fabricCanvasRef.current) return;

      const canvas = createCanvas(canvasRef.current);
      fabricCanvasRef.current = canvas;

      await loadImageToCanvas(canvas, data.viewer.imageUrl);

      // Apply initial filters
      if (currentImageRef.current) {
        applyFilters();
      }
    };

    initializeCanvas();

    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
  }, [data, applyFilters]);

  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const handleSave = async (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => {
    setBrightness(values.brightness);
    setContrast(values.contrast);
    setSaturation(values.saturation);
    setRotation(values.rotation);
    await saveImageConfig(data.id, values);
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <canvas ref={canvasRef} id="image-canvas" />

      <FabricToolbar
        initialValues={{
          brightness: data.viewer.configs.brightness,
          contrast: data.viewer.configs.contrast,
          saturation: data.viewer.configs.saturation,
          rotation: data.viewer.configs.rotation,
        }}
        onSave={handleSave}
        fabricFilterMode={{
          containerId: "image-viewer-container",
        }}
        enableAnnotations={true}
        containerId="image-viewer-container"
      />
    </div>
  );
}
