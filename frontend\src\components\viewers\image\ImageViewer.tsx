import { useRef, useEffect } from "react";
import { saveImageConfig } from "@/shared/api";
import { MedicalImageViewerProps } from "@/shared/types";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import { removeAnnotations } from "@/lib/fabric";
import * as fabric from "fabric";

export default function ImageViewer({ data }: MedicalImageViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const imageCanvasRef = useRef<HTMLCanvasElement | null>(null);

  const createCanvas = (canvasElement: HTMLCanvasElement): fabric.Canvas => {
    const container = canvasElement.parentElement;
    const width = container?.clientWidth || window.innerWidth;
    const height = container?.clientHeight || window.innerHeight;

    const finalWidth = Math.max(width, 100);
    const finalHeight = Math.max(height, 100);

    const canvas = new fabric.Canvas(canvasElement, {
      width: finalWidth,
      height: finalHeight,
      backgroundColor: "#000",
    });

    return canvas;
  };

  const loadImageToCanvas = async (
    canvas: fabric.Canvas,
    imageUrl: string
  ): Promise<void> => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const img = await (fabric as any).FabricImage.fromURL(imageUrl, {
      crossOrigin: "anonymous",
    });

    canvas.clear();
    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();
    const imgWidth = img.width || 1;
    const imgHeight = img.height || 1;
    const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight);

    img.scale(scale);
    img.set({
      left: canvasWidth / 2,
      top: canvasHeight / 2,
      originX: "center",
      originY: "center",
      selectable: false,
      evented: false,
    });

    canvas.add(img);
    canvas.renderAll();

    // Store reference for image rendering (not annotations)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerImageCanvas = canvas;
  };

  const initializeFabricOverlay = (imageCanvas: HTMLCanvasElement) => {
    // Create annotation canvas that overlays the image canvas - EXACT COPY FROM STACKVIEWER
    const container = document.getElementById("image-viewer-container");
    if (!container) return;

    // Remove existing annotation canvas
    const existingCanvas = document.getElementById("image-viewer-annotations");
    if (existingCanvas) {
      existingCanvas.remove();
    }

    // Create new annotation canvas
    const annotationCanvas = document.createElement("canvas");
    annotationCanvas.id = "image-viewer-annotations";
    annotationCanvas.style.position = "absolute";
    annotationCanvas.style.left = "0";
    annotationCanvas.style.top = "0";
    annotationCanvas.style.zIndex = "1000";
    annotationCanvas.style.pointerEvents = "auto";
    annotationCanvas.style.backgroundColor = "transparent";
    annotationCanvas.style.width = "100%";
    annotationCanvas.style.height = "100%";

    // Get the image element (parent of the canvas) - SAME AS STACKVIEWER
    const imageElement = imageCanvas.parentElement;

    if (imageElement) {
      // Insert the annotation canvas directly into the same parent as the image canvas
      imageElement.style.position = "relative";
      imageElement.appendChild(annotationCanvas);

      // Match the image canvas dimensions exactly
      const imageRect = imageCanvas.getBoundingClientRect();
      annotationCanvas.width = imageCanvas.width || imageRect.width;
      annotationCanvas.height = imageCanvas.height || imageRect.height;
    } else {
      // Fallback: add to container
      container.appendChild(annotationCanvas);
      const imageRect = imageCanvas.getBoundingClientRect();
      annotationCanvas.width = imageRect.width;
      annotationCanvas.height = imageRect.height;
    }

    // Initialize Fabric.js on the annotation canvas - EXACT COPY FROM STACKVIEWER
    const fabricCanvas = new fabric.Canvas("image-viewer-annotations", {
      backgroundColor: "transparent",
      selection: false,
    });

    // Store reference for the FabricToolbar to use - EXACT COPY FROM STACKVIEWER
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerFabricCanvas = fabricCanvas;

    // Sync canvas size when viewport changes - EXACT COPY FROM STACKVIEWER
    const syncCanvasSize = () => {
      // Match the canvas dimensions exactly
      annotationCanvas.width =
        imageCanvas.width || imageCanvas.getBoundingClientRect().width;
      annotationCanvas.height =
        imageCanvas.height || imageCanvas.getBoundingClientRect().height;

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const fabricCanvas = (window as any).imageViewerFabricCanvas;
      if (fabricCanvas) {
        fabricCanvas.setDimensions({
          width: annotationCanvas.width,
          height: annotationCanvas.height,
        });

        fabricCanvas.renderAll();
      }
    };

    // Listen for resize events - EXACT COPY FROM STACKVIEWER
    window.addEventListener("resize", syncCanvasSize);
  };

  useEffect(() => {
    if (!canvasRef.current || fabricCanvasRef.current) return;

    const initializeCanvas = async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (!canvasRef.current || fabricCanvasRef.current) return;

      const canvas = createCanvas(canvasRef.current);
      fabricCanvasRef.current = canvas;

      await loadImageToCanvas(canvas, data.viewer.imageUrl);

      // Apply initial CSS filters to the canvas - same as StackViewer approach
      const { brightness, contrast, saturation, rotation } =
        data.viewer.configs;
      canvasRef.current.style.filter = `brightness(${brightness}) contrast(${contrast}) saturate(${saturation})`;
      canvasRef.current.style.transform = `rotate(${rotation}deg)`;

      // Store canvas reference exactly like StackViewer does
      const imageCanvas = canvasRef.current;
      if (imageCanvas) {
        imageCanvasRef.current = imageCanvas;

        // Initialize Fabric.js overlay for annotations - EXACT COPY FROM STACKVIEWER
        setTimeout(() => {
          initializeFabricOverlay(imageCanvas);
        }, 500);
      }
    };

    initializeCanvas();

    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }

      // Clean up annotations - EXACT COPY FROM STACKVIEWER
      removeAnnotations("image-viewer-container");
    };
  }, [data]);

  const handleSave = async (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => {
    await saveImageConfig(data.id, values);
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <canvas ref={canvasRef} id="image-canvas" />

      <FabricToolbar
        initialValues={{
          brightness: data.viewer.configs.brightness,
          contrast: data.viewer.configs.contrast,
          saturation: data.viewer.configs.saturation,
          rotation: data.viewer.configs.rotation,
        }}
        onSave={handleSave}
        cssFilterMode={{
          targetElement: () => imageCanvasRef.current,
        }}
        enableAnnotations={true}
        containerId="image-viewer-container"
      />
    </div>
  );
}
