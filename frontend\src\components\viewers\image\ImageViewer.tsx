import { useRef, useEffect } from "react";
import { saveImageConfig } from "@/shared/api";
import { MedicalImageViewerProps } from "@/shared/types";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import * as fabric from "fabric";

export default function ImageViewer({ data }: MedicalImageViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);

  const createCanvas = (canvasElement: HTMLCanvasElement): fabric.Canvas => {
    const container = canvasElement.parentElement;
    const width = container?.clientWidth || window.innerWidth;
    const height = container?.clientHeight || window.innerHeight;

    const finalWidth = Math.max(width, 100);
    const finalHeight = Math.max(height, 100);

    const canvas = new fabric.Canvas(canvasElement, {
      width: finalWidth,
      height: finalHeight,
      backgroundColor: "#000",
    });

    return canvas;
  };

  const loadImageToCanvas = async (
    canvas: fabric.Canvas,
    imageUrl: string
  ): Promise<void> => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const img = await (fabric as any).FabricImage.fromURL(imageUrl, {
      crossOrigin: "anonymous",
    });

    canvas.clear();
    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();
    const imgWidth = img.width || 1;
    const imgHeight = img.height || 1;
    const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight);

    img.scale(scale);
    img.set({
      left: canvasWidth / 2,
      top: canvasHeight / 2,
      originX: "center",
      originY: "center",
      selectable: false,
      evented: false,
    });

    canvas.add(img);
    canvas.renderAll();

    // Store reference for FabricToolbar
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).imageViewerFabricCanvas = canvas;
  };

  useEffect(() => {
    if (!canvasRef.current || fabricCanvasRef.current) return;

    const initializeCanvas = async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (!canvasRef.current || fabricCanvasRef.current) return;

      const canvas = createCanvas(canvasRef.current);
      fabricCanvasRef.current = canvas;

      await loadImageToCanvas(canvas, data.viewer.imageUrl);

      // Apply initial CSS filters to the canvas - same as StackViewer approach
      const { brightness, contrast, saturation, rotation } = data.viewer.configs;
      canvasRef.current.style.filter = `brightness(${brightness}) contrast(${contrast}) saturate(${saturation})`;
      canvasRef.current.style.transform = `rotate(${rotation}deg)`;
    };

    initializeCanvas();

    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
  }, [data]);

  const handleSave = async (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => {
    await saveImageConfig(data.id, values);
  };

  return (
    <div className="image-viewer" id="image-viewer-container">
      <canvas ref={canvasRef} id="image-canvas" />

      <FabricToolbar
        initialValues={{
          brightness: data.viewer.configs.brightness,
          contrast: data.viewer.configs.contrast,
          saturation: data.viewer.configs.saturation,
          rotation: data.viewer.configs.rotation,
        }}
        onSave={handleSave}
        cssFilterMode={{
          targetElement: () => canvasRef.current,
        }}
        enableAnnotations={true}
        containerId="image-viewer-container"
      />
    </div>
  );
}
